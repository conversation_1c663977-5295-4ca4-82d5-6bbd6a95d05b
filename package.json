{"name": "vben-admin", "version": "2.11.5", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": {"url": "https://github.com/vbenjs/vue-vben-admin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"bootstrap": "pnpm install", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build", "build:analyze": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode analyze", "build:docker": "vite build --mode docker", "build:no-cache": "pnpm store prune && npm run build", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode test", "commit": "czg", "dev": "pnpm vite", "preinstall": "npx only-allow pnpm", "postinstall": "turbo run stub", "lint": "turbo run lint", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write .", "lint:stylelint": "stylelint \"**/*.{vue,css,less,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepare": "husky install", "preview": "npm run build && vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "serve": "npm run dev", "test:gzip": "npx http-server dist --cors --gzip -c-1", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"], "*.{scss,less,styl,html}": ["prettier --write", "stylelint --fix"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@iconify/iconify": "^3.1.1", "@logicflow/core": "^1.2.26", "@logicflow/extension": "^1.2.26", "@vben/hooks": "workspace:*", "@vue/shared": "^3.4.25", "@vueuse/core": "^10.9.0", "@zxcvbn-ts/core": "^3.0.4", "ant-design-vue": "^4.2.6", "axios": "^1.6.8", "codemirror": "^5.65.16", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.5.0", "exceljs": "^4.4.0", "html2canvas": "^1.4.1", "lodash-es": "^4.17.21", "motion-v": "^1.4.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.2", "pinia": "2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "pretty-bytes": "^7.0.0", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qs": "^6.12.1", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sortablejs": "^1.15.2", "tinymce": "^5.10.9", "unocss": "^0.59.4", "vditor": "^3.10.4", "vue": "^3.5.13", "vue-i18n": "^9.13.1", "vue-json-pretty": "^2.4.0", "vue-router": "4.3.2", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@iconify/json": "^2.2.203", "@purge-icons/generated": "^0.10.0", "@types/codemirror": "^5.60.15", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.15", "@types/showdown": "^2.0.6", "@types/sortablejs": "^1.15.8", "@vben/eslint-config": "workspace:*", "@vben/stylelint-config": "workspace:*", "@vben/ts-config": "workspace:*", "@vben/types": "workspace:*", "@vben/vite-config": "workspace:*", "@vue/compiler-sfc": "^3.4.25", "@vue/test-utils": "^2.4.5", "conventional-changelog-cli": "^4.1.0", "cross-env": "^7.0.3", "cz-git": "^1.9.1", "czg": "^1.9.1", "husky": "^9.0.11", "lint-staged": "15.2.2", "prettier": "^3.2.5", "prettier-plugin-packagejson": "^2.5.0", "rimraf": "^5.0.5", "turbo": "^1.13.2", "typescript": "^5.4.5", "unbuild": "^2.0.0", "vite": "^5.4.19", "vue-tsc": "^2.0.14", "vue3-tree-org": "^4.2.2"}, "packageManager": "pnpm@9.0.4", "engines": {"node": ">=18.12.0", "pnpm": ">=9.0.2"}}