import type { ComputedRef, Ref } from 'vue';

export type DynamicProps<T> = {
  [P in keyof T]: Ref<T[P]> | T[P] | ComputedRef<T[P]>;
};

export type CreateMapParamsExtend = object | string | array | undefined | number | boolean | null;

export type CreateMapParams = [any, string | [string, string?], string, CreateMapParamsExtend?];

// 返回值
export interface CreateMapReturnValue {
  value: any;
  label: string;
  color: string;
  extend: any;
}

export type CreateMapReturn = CreateMapReturnValue[];

export type CreateMapReturnI18n = {
  value: any;
  label: string | Ref<string> | ComputedRef<string>;
  color: string;
  extend: any;
}[];

// step 配置
export interface DefinitionStepsValue {
  title: string;
  types: string[];
}

export type DefinitionSteps = DefinitionStepsValue[];
