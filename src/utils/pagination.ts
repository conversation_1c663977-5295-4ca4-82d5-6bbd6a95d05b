// 导入依赖
// apiMethod: (pagination: any) => Promise<any>, params?: any
import { isPlainObject, isBoolean } from 'lodash-es';
// 需要传入的参数接口类型
export interface PaginationConfig {
  api: (pagination: any) => Promise<any>;
  params?: any;
  pageSize?: number;
  model?: boolean;
  // 是否立即获取数据
  immediate?: boolean;
}

export interface RefreshParams {
  [key: string]: any;
}

export class PaginationData<T> {
  private currentPageData: T[] = [];
  private apiMethod: (pagination: any) => Promise<any>;
  private current = 1; // 当前页码
  private size = 10; // 每页数据大小
  private total = 0; // 总条数
  private pages = 0; // 总页数
  private params: any = {}; // 请求参数
  // 模式 是否需要拼接数据 默认为true 需要拼接 false 不需要拼接
  // false 适用于antd的分页组件, true 适用于上拉加载更多
  private model = true; // 默认为true 上拉加载更多模式

  constructor({ api, params, pageSize, model }: PaginationConfig) {
    this.apiMethod = api;
    if (isPlainObject(params)) {
      this.params = params;
    }
    if (pageSize) {
      this.size = pageSize;
    }
    if (isBoolean(model)) {
      this.model = model;
    }
  }

  // 刷新方法
  public async refresh(params?: RefreshParams): Promise<T[]> {
    this.current = 1; // 重置为第一页
    if (isPlainObject(params)) {
      this.params = params;
    }
    const response = await this.apiMethod(
      Object.assign(
        {
          current: this.current,
          size: this.size,
        },
        this.params,
      ),
    );
    return this.setResponseData(response);
  }

  // 翻页方法
  public async nextPage(): Promise<T[]> {
    this.current++;
    const response = await this.apiMethod(
      Object.assign(
        {
          current: this.current,
          size: this.size,
        },
        this.params,
      ),
    );
    return this.setResponseData(response);
  }
  public setResponseData(response: any) {
    if (response) {
      if (this.model) {
        this.currentPageData = this.currentPageData.concat(response.records);
      } else {
        this.currentPageData = response.records;
      }

      this.total = response.total;
      this.pages = response.pages;
      this.current = response.current;
      // 对pageNo进行校验
      if (this.current > this.pages) {
        this.current = this.pages;
      }
    }
    return this.currentPageData;
  }

  // 使用页码跳转
  public async goPage({ current, pageSize }: { current: number; pageSize?: number }): Promise<T[]> {
    if (this.model) {
      new Error('请在model为false的情况下使用该方法');
      return [];
    }
    current && (this.current = current);
    pageSize && (this.size = pageSize);
    const response = await this.apiMethod(
      Object.assign(
        {
          current: this.current,
          size: this.size,
        },
        this.params,
      ),
    );
    // if (response) {
    //   this.currentPageData = response.records;
    //   this.total = response.total;
    //   this.pages = response.pages;
    //   this.current = response.current;
    //   // 对pageNo进行校验
    //   if (this.current > this.pages) {
    //     this.current = this.pages;
    //   }
    // }
    // return this.currentPageData;
    return this.setResponseData(response);
  }
  // 获取当前页码
  public getPage(): number {
    return this.current;
  }

  // 获取每页数据大小
  public getPageSize(): number {
    return this.size;
  }

  // 设置每页数据大小
  public setPageSize(size: number): void {
    this.size = size;
  }
  // 是否有下一页
  public hasNextPage(): boolean {
    return this.current <= this.pages;
  }
  // 获取总多少条数据
  public getTotal(): number {
    return this.total;
  }
  // 返回antd的分页组件需要的数据
  public getAntdPaginationData() {
    return {
      current: this.current,
      pageSize: this.size,
      total: this.total,
    };
  }
}
