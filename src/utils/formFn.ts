import dayjs from 'dayjs';

/**
 * 表单的一些常用的 数据处理方法
 */

/**
 *  ApiTree
 *
 *   checkable: true
 *   checkStrictly: true
 * @description 受控 上层节点取消 自动取消子节点  上层节点新增 自动勾选子节点 子节点取消 不影响 父节点
 * @param {Array} value ,
 * @param {object} events
 * @returns value
 */
export function apiTreeCheckStrictlyAuto({ value, events, valueField }) {
  const checkedKeys = value.checked;
  const { node, event, checked } = events;
  const childrenKeys = getChildKeys(node.children || [], valueField);
  if (event === 'check' && checked) {
    value = Array.from(new Set([...checkedKeys, ...childrenKeys]));
  } else if (event === 'check') {
    value = checkedKeys.filter((item) => !childrenKeys.includes(item));
  }

  return value;
}
export function apiTreeCheckStrictlyAuto1({ value }) {
  return value.checked;
}
// 获取子节点 id
export function getChildKeys(tree = [], valueField) {
  const keys: string[] = [];
  tree.forEach((item: any) => {
    item[valueField] && keys.push(item[valueField]);
    if (item.children) {
      const _keys = getChildKeys(item.children, valueField);
      _keys.length > 0 && keys.push(..._keys);
    }
  });
  return keys;
}

// 字符串转数组
export function strToArr(str: string): Array<any> {
  // 判断是不是数组
  if (Array.isArray(str)) {
    return str;
  }
  if (typeof str === 'string' && str.length > 0) {
    return str.split(',');
  }
  return [];
}

// 数组转字符串
export function arrToStr(arr: Array<any>): string {
  // 判断是不是字符串
  if (typeof arr === 'string') {
    return arr;
  }
  if (Array.isArray(arr) && arr.length > 0) {
    return arr.join(',');
  }
  return '';
}

export function transformRecordByKeys(
  record: Record<string, any>,
  keys: string[],
  direction: 'toArr' | 'toStr',
): Record<string, any> {
  const transform = direction === 'toArr' ? strToArr : arrToStr;

  keys.forEach((key) => {
    if (record[key]) {
      record[key] = transform(record[key]);
    } else if (record[key] === null) {
      record[key] = [];
    }
  });

  return record;
}

// RangePicker 日期范围处理
export function transformRangePicker({ value }: any) {
  if (!value) return null;
  const [start, end] = value;
  const newStart = dayjs(start).format('YYYY-MM-DD 00:00:00');
  const newEnd = dayjs(end).format('YYYY-MM-DD 23:59:59');
  return [newStart, newEnd];
}
