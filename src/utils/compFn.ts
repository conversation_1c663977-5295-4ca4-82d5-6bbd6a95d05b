import { uuid } from '@/utils/uuid';
import { createVNode, render, VNode, Component, nextTick } from 'vue';

// ✅ 基础可拓展 props 类型
type BaseProps = {
  title?: string;
  close?: () => void;
  onSuccess?: (result?: any) => void;
  onError?: (error?: any) => void;
  destroy?: () => void;
  el?: HTMLElement;
  transitionSelector?: string;
  options?: Record<string, any>;
};

// ✅ 返回值结构
type CreateFuncCompReturn = {
  close: () => Promise<void>;
};

// ✅ 主函数，使用泛型参数
function createFuncComp<P extends BaseProps = BaseProps>(
  Components: Component,
  transitionSelector: string = '',
) {
  return function (option: P = {} as P): CreateFuncCompReturn {
    const name = Components.name || 'anonymous';
    const uuidStr = `${uuid('xxxxxxxxxxxxxxxxxxxx')}-${name}`;

    const div = document.createElement('div');
    div.className = uuidStr;
    document.body.appendChild(div);

    let hasClosed = false;
    let transitionEnded = false;

    const cleanup = () => {
      if (!div.isConnected) return;
      render(null, div);
      div.parentElement?.removeChild(div);
    };

    const destroy = () => {
      const selector = transitionSelector;
      if (!selector) {
        setTimeout(() => {
          cleanup();
        }, 1500);
        return;
      }
      const transitionEl = div.querySelector(selector) as HTMLElement | null;

      if (transitionEl) {
        const handleTransitionEnd = () => {
          if (transitionEnded) return;
          transitionEnded = true;
          transitionEl.removeEventListener('animationend', handleTransitionEnd);
          transitionEl.removeEventListener('transitionend', handleTransitionEnd);
          cleanup();
        };

        transitionEl.addEventListener('animationend', handleTransitionEnd, { once: true });
        transitionEl.addEventListener('transitionend', handleTransitionEnd, { once: true });

        setTimeout(() => {
          if (!transitionEnded) {
            handleTransitionEnd();
          }
        }, 1500);
      } else {
        setTimeout(() => {
          cleanup();
        }, 1500);
      }
    };

    const props: P = {
      ...option,
      destroy,
    };

    const vnode: VNode = createVNode(Components, props);
    render(vnode, div);

    async function close(): Promise<void> {
      if (hasClosed) return;
      hasClosed = true;

      await nextTick();

      const instance = vnode.component;
      const exposed = instance?.exposed;

      if (typeof exposed?.beforeClose === 'function') {
        exposed.beforeClose();
      }

      destroy();
    }

    return { close };
  };
}

export { createFuncComp };
export type { BaseProps as Props };
