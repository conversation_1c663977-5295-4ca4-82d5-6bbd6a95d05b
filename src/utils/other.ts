import { isJson } from './is';
import { cloneDeep, omit, trim, map, isString, isNumber, get, isEmpty, first } from 'lodash-es';
import { CreateMapParams, CreateMapReturn } from '#/utils';

/**
 * 休眠 ms 毫秒 用于异步等待
 * @param {number} ms  毫秒
 * @returns {Promise}
 */
export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 *   enum 转换为数组
 * @param {any} enumObj
 * @returns {any[]}
 */
export function enumToArray(
  enumObj: any,
  enumColor?: any,
): { label: string; value: string; color: string }[] {
  if (enumColor) {
    return Object.keys(enumObj).map((key) => ({
      label: enumObj[key],
      value: key,
      color: enumColor[key] || '#44566C',
    }));
  } else {
    return Object.keys(enumObj).map((key) => ({
      label: enumObj[key],
      value: key,
      color: '#44566C',
    }));
  }
}

/**
 *  enum 反向映射
 */
export function enumReverseMapping(enumObj: any) {
  const result: any = {};
  Object.keys(enumObj).forEach((key) => {
    result[enumObj[key]] = key;
  });
  return result;
}

/**
 * enum 转换为 map
 * @param {any} enumObj
 **/
export function enumToMap(enumObj: any) {
  const result: any = {};
  Object.keys(enumObj).forEach((key) => {
    result[key] = key;
  });
  return result;
}

/**
 *
 * @param record
 * @param keys
 * @returns
 * @description: 合并表单数据
 */
export function mergeFormKeys(record, keys) {
  const value = cloneDeep(record);
  const delKey: string[] = [];
  for (let index = 0; index < keys.length; index++) {
    const key = keys[index];
    value[key] = [];
    const keyArr = tryDeconstructArray(key);
    keyArr.forEach((v, i) => {
      value[key][i] = value[v] || '';
      delKey.push(v);
    });
  }
  // 表单转换
  return omit(value, delKey);
}

export function JsonToArr(str: string): Array<any> {
  if (isJson(str)) {
    return JSON.parse(str);
  } else {
    return [];
  }
}

function tryDeconstructArray(key: string): Array<string> {
  const pattern = /^\[(.+)\]$/;
  if (pattern.test(key)) {
    const match = key.match(pattern);
    if (match && match[1]) {
      const keys = match[1].split(',');
      // 除去空格
      return map(keys, trim);
    }
  }
  return [];
}

// 将两个字符串都转为大写，然后比较
export function compareString(str1: string, str2: string) {
  if (typeof str1 !== 'string' || typeof str2 !== 'string') return false;
  return str1.toUpperCase() === str2.toUpperCase();
}

// 第一项是字符串或者数字，都转为大写字符串，第二项是数组，判断字符串是否在数组中 都转为大写字符串
export function compareStringInArray(str: string | number, arr: Array<any>) {
  if (!isString(str) && !isNumber(str)) return false;
  if (!Array.isArray(arr)) return false;
  const arrUpper = arr.map((item) => {
    if (isString(item) || isNumber(item)) {
      return item.toString().toUpperCase();
    }
    return item;
  });
  return arrUpper.includes(str.toString().toUpperCase());
}

// 保留两位小数 ，可能是数字，也可能是字符串，或者其他类型 做好容错处理 数值为空时返回0.00
export function toDecimal2(x: any) {
  if (x === null || x === undefined) {
    return '0.00';
  }
  let f = parseFloat(x);
  if (isNaN(f)) {
    return '0.00';
  }
  f = Math.round(x * 100) / 100;
  const s = f.toString();
  const rs = s.indexOf('.');
  if (rs < 0) {
    return s + '.00';
  }
  return s;
}

// 写一个方法，接收一个promise方法，请求获取全部分页数据
export async function getAllPageData<T>(
  request: (params: any) => Promise<T>,
  params: any = {},
  key = 'records',
): Promise<T> {
  try {
    let data: any = [];
    let current = 1; // 当前页
    let totalPage = 1; // 总页数
    const pageSize = 1000; // 分页大小
    while (current <= totalPage) {
      const res = await request({ ...params, current, size: pageSize });
      if (res) {
        data = data.concat(res[key]);
        totalPage = res['pages'] || 0;
        current++;
      }
    }
    return Promise.resolve(data);
  } catch (error) {
    return Promise.reject(error);
  }
}

export function calculateDivisions(num: number, divisor: number, max = 0) {
  if (divisor === 0) {
    throw new Error('除数不能为0');
  }
  const result = num / divisor;
  if (!result) return 0;
  // 计算result保留整数
  const resultInt = Math.floor(result);
  if (max > 0) {
    return resultInt;
  } else {
    // 判断 resultInt 是否大于 max ，大于则返回 max ，否则返回 resultInt
    return resultInt > max ? max : resultInt;
  }
}

export function createMap(list: CreateMapParams[]): CreateMapReturn {
  return map(list, ([value, label, color, extend]) => {
    if (typeof label === 'string') {
      return { value, label, color, extend };
    }
    return { value, label: first(label), color, extend };
  });
}

export class TimedTask {
  private timerId: NodeJS.Timeout | null = null;
  private interval: number = 5000; // 默认间隔时间为5秒，根据实际需求调整
  private apiRequest: () => Promise<any>; // 接口函数
  private shouldContinue: boolean = false; // 新增标志位

  constructor(apiRequest: () => Promise<any>, interval: number = 5000) {
    this.apiRequest = apiRequest;
    this.interval = interval;
  }

  startTask(): void {
    console.log('Start task...');
    this.shouldContinue = true;
    this.executeTask(); // 第一次立即执行任务
  }

  private executeTask(): void {
    if (!this.shouldContinue) {
      return; // 不再执行任务
    }

    // 在执行任务前验证是否应该继续
    this.apiRequest().finally(() => {
      // 在接口请求结束后，等待稳定的间隔时间再执行下一次任务
      this.timerId = setTimeout(() => {
        this.executeTask();
      }, this.interval);
    });
  }

  endTask(): void {
    console.log('Stopping task...');
    this.shouldContinue = false;
    // 清除定时器
    this.clearTimer();
  }

  private clearTimer(): void {
    if (this.timerId !== null) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
  }
}

// // 使用示例
// const apiRequest = () => {
//   // 这里是你的接口请求逻辑，使用实际的请求方法和地址
//   return fetch('https://api.example.com/data').then((response) => response.json());
// };

// const timedTask = new TimedTask(apiRequest, 5000); // 传入接口函数和间隔时间
// timedTask.startTask();

// // 例如，任务执行10秒后结束
// setTimeout(() => {
//   timedTask.endTask();
// }, 10000);

/**
 * @description: 默认对象字段有 province  city districts  street 转化成省市区街道拼接 或 其他拼接
 */

export function concatenateAddress(
  address: any,
  addressKeys: string[] = ['province', 'city', 'district', 'street'],
  separator = '/',
): string | undefined {
  if (isEmpty(address)) {
    return '-';
  }
  // 判断 keys 是否为空 不能少于四个
  if (isEmpty(addressKeys)) {
    return '-';
  }

  const showList: string[] = [];

  addressKeys.forEach((item) => {
    const value = get(address, item);
    if (value) {
      showList.push(value);
    }
  });

  return showList.length > 0 ? showList.join(separator) : '-';
}

/**
 * @description: 字符串解析成对象地址  省市区街道拼接
 */
export function parseAddress(
  address: string,
  addressKeys: string[] = ['province', 'city', 'district', 'street'],
): any {
  if (isEmpty(address)) {
    return {};
  }
  const addressList = address.split('/');
  return addressKeys.reduce((acc, cur, index) => {
    acc[cur] = addressList[index] ?? '';
    return acc;
  }, {});
}
