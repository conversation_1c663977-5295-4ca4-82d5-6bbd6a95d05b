// @ts-nocheck
/**
 * 判断是否为空
 * @param val 数据
 */
export const validateNull = (val: any) => {
  if (typeof val === 'boolean') {
    return false;
  }
  if (typeof val === 'number') {
    return false;
  }
  if (val instanceof Array) {
    if (val.length === 0) return true;
  } else if (val instanceof Object) {
    if (JSON.stringify(val) === '{}') return true;
  } else {
    if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '')
      return true;
    return false;
  }
  return false;
};

export const rule = {
  /**
   * 校验用户输入的长度避免超长
   *  0-255个字符
   *  超长
   */
  overLength(rule: any, value: any) {
    if (value?.length > 255) {
      return Promise.reject(new Error('输入内容过长，请重新输入'));
    } else {
      return Promise.resolve();
    }
  },
  /**
   * 校验 请输入中文、英文、数字包括下划线
   * 名称校验
   */
  validatorNameCn(rule: any, value: any): Promise<void> {
    const acount = /^[\u4E00-\u9FA5A-Za-z0-9_]+$/;
    if (value && !acount.test(value)) {
      return Promise.reject(new Error('请输入中文、英文、数字包括下划线'));
    } else {
      return Promise.resolve();
    }
  },
  /**
   * 校验 请输入大写英文、下划线
   * 名称校验
   */
  validatorCapital(rule: any, value: any): Promise<void> {
    return new Promise((resolve, reject) => {
      const acount = /^[A-Z_]+$/;
      if (value && !acount.test(value)) {
        return reject(new Error('请输入大写英文、下划线'));
      } else {
        return resolve();
      }
    });
  },

  /**
   * 校验 请输入小写英文、下划线
   * 名称校验
   */
  validatorLowercase(rule: any, value: any): Promise<void> {
    const acount = /^[a-z_]+$/;
    if (value && !acount.test(value)) {
      return Promise.reject(new Error('请输入小写英文、下划线'));
    } else {
      return Promise.resolve();
    }
  },

  /**
   * 校验 请输入小写英文
   * 名称校验
   */
  validatorLower(rule: any, value: any): Promise<void> {
    const acount = /^[a-z]+$/;
    if (value && !acount.test(value)) {
      return Promise.reject(new Error('请输入小写英文'));
    } else {
      return Promise.resolve();
    }
  },

  /**
   * 校验 请输入小写英文、数字
   */
  async validatorLowerNumber(rule: any, value: any) {
    const count = /^[a-z0-9]+$/;
    if (value && !count.test(value)) {
      return Promise.reject(new Error('请输入小写英文、数字'));
    }
    return Promise.resolve();
  },

  /**
   * 校验首尾空白字符的正则表达式
   *
   */
  checkSpace(rule: any, value: any): Promise<void> {
    const longrg = /[^\s]+$/;
    if (!longrg.test(value)) {
      return Promise.reject(new Error('请输入非空格信息'));
    } else {
      return Promise.resolve();
    }
  },

  /**
   * 校验手机号
   */
  async validatePhone(rule: any, value: any) {
    // const isPhone = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/;
    // 宽松手机号正则 支持国际化手机号
    const isPhone = /^1[3456789]\d{9}$/;

    if (value.indexOf('****') >= 0) {
      return Promise.resolve();
    }

    if (!isPhone.test(value)) {
      return Promise.reject(new Error('请输入正确的手机号'));
    } else {
      return Promise.resolve();
    }
  },

  /* 数字 */
  number(rule, value): Promise<void> {
    return validateFnPromise('number', rule, value, '包含非数字字符');
  },

  /* 字母 */
  letter(rule, value): Promise<void> {
    return validateFnPromise('letter', rule, value, '包含非字母字符');
  },

  /* 字母和数字 */
  letterAndNumber(rule, value): Promise<void> {
    return validateFnPromise('letterAndNumber', rule, value, '只能输入字母或数字');
  },

  /* 手机号码 */
  mobilePhone(rule, value): Promise<void> {
    return validateFnPromise('mobilePhone', rule, value, '手机号码格式有误');
  },

  /* 字母开头，仅可包含数字 */
  letterStartNumberIncluded(rule, value): Promise<void> {
    return validateFnPromise(
      'letterStartNumberIncluded',
      rule,
      value,
      '必须以字母开头，可包含数字',
    );
  },

  /* 禁止中文输入 */
  noChinese(rule, value): Promise<void> {
    return validateFnPromise('noChinese', rule, value, '不可输入中文字符');
  },

  /* 必须中文输入 */
  chinese(rule, value): Promise<void> {
    return validateFnPromise('chinese', rule, value, '只能输入中文字符');
  },

  /* 电子邮箱 */
  email(rule, value): Promise<void> {
    return validateFnPromise('email', rule, value, '邮箱格式有误');
  },

  /* URL网址 */
  url(rule, value): Promise<void> {
    return validateFnPromise('url', rule, value, 'URL格式有误');
  },

  /* json 格式 */
  json(rule, value): Promise<void> {
    if (validateNull(value) || value.length <= 0) {
      return Promise.resolve();
    }

    try {
      JSON.parse(value);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(new Error('json 格式有误'));
    }
  },

  regExp(rule, value): Promise<void> {
    if (validateNull(value) || value.length <= 0) {
      return Promise.resolve();
    }

    const pattern = new RegExp(rule.regExp);

    if (!pattern.test(value)) {
      const errTxt = rule.errorMsg || 'invalid value';
      return Promise.reject(new Error(errTxt));
    } else {
      return Promise.resolve();
    }
  },
};

/**
 * @desc  [自定义校验规则]
 * @example
 *  import { validateRule } from "@/utils/validateRules";
 *  rules: [
 *     { validator: validateRule.emailValue, trigger: 'blur'}
 *  ]
 */

export const getRegExp = function (validatorName) {
  const commonRegExp = {
    number: '^[-]?\\d+(\\.\\d+)?$',
    letter: '^[A-Za-z]+$',
    letterAndNumber: '^[A-Za-z0-9]+$',
    mobilePhone: '^[1][3-9][0-9]{9}$',
    letterStartNumberIncluded: '^[A-Za-z]+[A-Za-z\\d]*$',
    noChinese: '^[^\u4e00-\u9fa5]+$',
    chinese: '^[\u4e00-\u9fa5]+$',
    email: '^([-_A-Za-z0-9.]+)@([_A-Za-z0-9]+\\.)+[A-Za-z0-9]{2,3}$',
    url: '(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]',
  };
  return commonRegExp[validatorName];
};

const validateFnPromise = (validatorName, rule, value, defaultErrorMsg): Promise<void> => {
  if (validateNull(value) || value.length <= 0) {
    return Promise.resolve();
  }

  const reg = new RegExp(getRegExp(validatorName));

  if (!reg.test(value)) {
    const errTxt = rule.errorMsg || defaultErrorMsg;
    return Promise.reject(new Error(errTxt));
  } else {
    return Promise.resolve();
  }
};
