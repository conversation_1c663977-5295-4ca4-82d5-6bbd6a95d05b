import { uuid } from '@/utils/uuid';

class PhotoCompress {
  private nextQ = 0.5; // 压缩比例
  private maxQ = 1;
  private minQ = 0;
  private minSize: number; // 最小大小
  private maxSize: number; // 最大大小

  constructor(minSize: number, maxSize: number) {
    this.minSize = minSize;
    this.maxSize = maxSize;
  }

  dataUrlToFile(base64Codes: string, fileName: string): File | null {
    const arr = base64Codes.split(',');
    if (arr.length < 2) {
      console.error('Invalid base64Codes format');
      return null;
    }

    const mimeMatch = arr[0].match(/:(.*?);/);
    if (!mimeMatch) {
      console.error('MIME type not found in base64Codes');
      return null;
    }

    const mime = mimeMatch[1];
    const bStr = atob(arr[1]);
    let n = bStr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bStr.charCodeAt(n);
    }
    return new File([u8arr], fileName, { type: mime });
  }

  compress(file: File | Blob, callback: (compressedFile: File | null) => void) {
    this.imgBase64(file, (_, canvas) => {
      const base64Codes = canvas.toDataURL('image/jpeg', this.nextQ); // 压缩为 JPEG
      const compressFile = this.dataUrlToFile(base64Codes, `${uuid()}.jpg`); // 转成 file 文件
      if (compressFile) {
        const compressFileSize = compressFile.size; // 压缩后文件大小（单位：字节）
        console.log('图片质量：' + this.nextQ);
        console.log('压缩后文件大小：' + compressFileSize / 1024 + ' KB');
        if (compressFileSize > this.maxSize) {
          // 压缩后文件大于最大值
          this.maxQ = this.nextQ;
          this.nextQ = (this.nextQ + this.minQ) / 2; // 质量降低
          this.compress(file, callback);
        } else if (compressFileSize < this.minSize) {
          // 压缩以后文件小于最小值
          this.minQ = this.nextQ;
          this.nextQ = (this.nextQ + this.maxQ) / 2; // 质量提高
          this.compress(file, callback);
        } else {
          callback(compressFile);
        }
      } else {
        // 处理 compressFile 为 null 的情况
        console.error('压缩后文件为空');
        callback(null);
      }
    });
  }

  imgBase64(
    file: File | Blob,
    callback: (image: HTMLImageElement, canvas: HTMLCanvasElement) => void,
  ) {
    if (!file || !window.FileReader) return;

    const image = new Image();
    image.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        canvas.width = image.width * this.nextQ; // 使用外部的 this
        canvas.height = image.height * this.nextQ; // 使用外部的 this
        ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
        callback(image, canvas);
      }
    };

    if (file instanceof File) {
      if (/^image/.test(file.type)) {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          // 使用箭头函数来保留外部的 this
          image.src = reader.result as string;
        };
      }
    } else if (file instanceof Blob) {
      const reader = new FileReader();
      reader.onload = (event) => {
        image.src = event.target?.result as string;
      };
      reader.readAsDataURL(file);
    }
  }
}

export { PhotoCompress };
