import { createMap } from '@/utils/other';
import { CreateMapParams } from '#/utils';

export const MenuTypeList: CreateMapParams[] = [
  ['0', '菜单', '#FFB000'],
  ['2', '详情', '#26C7B7'],
  ['1', '按钮', '#1F90FF'],
];

export const MenuTypeArray = createMap(MenuTypeList);

// 是否显示
export const IsShowListStr: CreateMapParams[] = [
  ['0', '否', '#FF4C52'],
  ['1', '是', '#26C7B7'],
];
export const IsShowStrArray = createMap(IsShowListStr);

// 是否显示
export const IsShowList: CreateMapParams[] = [
  [0, '否', '#FF4C52'],
  [1, '是', '#26C7B7'],
];
// 是否显示 boolean
export const IsShowBooleanList: CreateMapParams[] = [
  [false, '否', '#FF4C52'],
  [true, '是', '#26C7B7'],
];
export const IsShowBooleanArray = createMap(IsShowBooleanList);

export const IsShowArray = createMap(IsShowList);

export const StatusList: CreateMapParams[] = [
  [1, '启用', '#26C7B7'],
  [0, '禁用', '#FF4C52'],
];

// 是否启用 bool
export const IsEnableBooleanList: CreateMapParams[] = [
  [false, '禁用', '#FF4C52'],
  [true, '启用', '#26C7B7'],
];
export const IsEnableBooleanArray = createMap(IsEnableBooleanList);

export const StatusArray = createMap(StatusList);

// 数据范围
export const DsTypeList: CreateMapParams[] = [
  [0, '全部', '#FFB000'],
  [1, '自定义', '#1F90FF'],
  [2, '本级及子级', '#26C7B7'],
  [3, '本级', '#FF4C52'],
  [4, '本人', '#FF4C52'],
];

export const DsTypeArray = createMap(DsTypeList);
