<template>
  <div>
    <FormItemRest>
      <div class="InputList">
        <div class="InputList-item" v-for="(item, index) in state" :key="index">
          <div class="InputList-item-input">
            <Input :maxlength="20" v-model:value="item.name" :placeholder="placeholder" />
          </div>
          <div class="InputList-item-action" @click="method.remove(index)">
            <MinusCircleOutlined />
          </div>
        </div>
        <div class="InputList-item start">
          <Button type="primary" @click="method.add">新增</Button>
        </div>
      </div>
    </FormItemRest>
  </div>
</template>
<script lang="ts" setup>
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { toRaw, watch } from 'vue';
  import { Input, FormItemRest, Button, message } from 'ant-design-vue';
  import { MinusCircleOutlined } from '@ant-design/icons-vue';
  import { cloneDeep, size } from 'lodash-es';

  defineOptions({ name: 'InputList' });
  interface ValueObject {
    name: string;
    id?: string;
    apartmentId?: string;
  }
  type ValueState = ValueObject[];
  interface Props {
    value: ValueState;
    placeholder?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: () => [],
    placeholder: '请输入',
  });
  defineEmits(['change', 'update:value']);
  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', 'change');
  const method = {
    add() {
      const _state = cloneDeep(toRaw(state.value));
      _state.push({ name: '' });
      state.value = _state;
    },
    remove(index: number) {
      if (size(state.value) === 1) {
        message.error('至少保留一个');
        return;
      }
      const _state = cloneDeep(toRaw(state.value));
      _state.splice(index, 1);
      state.value = _state;
    },
  };
  watch(
    () => props.value,
    (v) => {
      if (size(v) === 0) {
        method.add();
      }
    },
    {
      immediate: true,
    },
  );
</script>

<style lang="less" scoped>
  .InputList {
    display: flex;
    flex-direction: column;
    gap: 10px;

    &-item {
      display: flex;
      gap: 5px;

      &.start {
        justify-content: flex-start;
      }

      &-input {
        flex-grow: 1;
      }

      &-action {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 40px;
        color: @primary-color;
        font-size: 16px;
        // 手指
        cursor: pointer;

        &:hover {
          color: @primary-color;
        }

        &:active {
          color: @primary-color;
        }
      }
    }
  }
</style>
