<template>
  <div class="EvaluateRate">
    <div class="mt--3px">
      <Rate v-bind="omit(props, ['value', 'onUpdate:value'])" v-model:value="state" />
    </div>
    <div> {{ showEvaluateRateText }} </div>
  </div>
</template>

<script lang="ts" setup>
  import { Rate } from 'ant-design-vue';
  import type { RateProps } from 'ant-design-vue';
  import { computed } from 'vue';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { omit } from 'lodash-es';

  defineOptions({ name: 'EvaluateRate' });

  const props = withDefaults(defineProps<RateProps>(), {
    value: undefined,
  });

  defineEmits(['change', 'update:value']);
  const [state] = useRuleFormItem<RateProps, keyof RateProps, number>(props, 'value', [
    'update:value',
    'change',
  ]);

  // 评价等级文字
  const rateTexts = ['非常差', '差', '一般', '好', '非常好'];
  const showEvaluateRateText = computed(() => {
    if (props.value === undefined) return '未评价';
    if (props.value >= 0 && props.value <= 5) {
      const index = Math.min(Math.ceil(props.value) - 1, 4);
      return rateTexts[index];
    }
    return `${props.value}星`; // 如果值超出范围，显示原始值
  });
</script>

<style lang="less" scoped>
  .EvaluateRate {
    display: flex;
    align-items: center;
    // justify-content: center;
    gap: 28px;
  }
</style>
