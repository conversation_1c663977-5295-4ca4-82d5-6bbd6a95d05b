<script lang="ts" setup>
  import { InputNumber, FormItemRest, Radio } from 'ant-design-vue';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import type { InputNumberProps } from 'ant-design-vue';
  import { omit } from 'lodash-es';
  import { computed } from 'vue';

  defineOptions({ name: 'InputZero' });

  const props = withDefaults(defineProps<InputNumberProps>(), {
    value: undefined,
    bordered: true,
    controls: true,
  });
  const [state] = useRuleFormItem<InputNumberProps, keyof InputNumberProps, number>(
    props,
    'value',
    ['update:value', 'change'],
  );

  defineEmits(['update:value', 'change']);

  // 选项保存的值
  const saveValue = computed({
    get: () => {
      if (state.value === 0) {
        return 0;
      }
      return 1;
    },
    set: (val: number) => {
      console.log('saveValue', val);
      state.value = val;
    },
  });
</script>

<template>
  <div>
    <FormItemRest>
      <div class="input-zero">
        <Radio.Group v-model:value="saveValue">
          <Radio.Button :value="0">未发券的</Radio.Button>
          <Radio.Button :value="1">券的发放数量</Radio.Button>
        </Radio.Group>
        <InputNumber
          v-show="saveValue !== 0"
          v-bind="omit(props, ['value', 'onUpdate:value'])"
          v-model:value="state"
          style="flex: 1"
        />
      </div>
    </FormItemRest>
  </div>
</template>

<style lang="less" scoped>
  .input-zero {
    display: flex;
    align-items: center;
    gap: 8px;
  }
</style>
