<template>
  <div class="DividerTitle" :class="{ 'DividerTitle-line': line }">
    <div class="DividerTitle-box"></div>
    <div class="text-lg font-bold flex-1">
      <slot v-if="$slots.default"></slot>
      <span v-else>{{ title }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  defineOptions({ name: 'DividerTitle' });
  type ValueState = string | undefined;
  interface Props {
    title?: ValueState;
    // 底部线
    line?: boolean;
  }
  withDefaults(defineProps<Props>(), {
    title: '',
    line: true,
  });
</script>

<style lang="less" scoped>
  .DividerTitle {
    display: flex;
    flex: 1 0 0;
    align-items: center;
    height: 44px;
    margin-bottom: 10px;
    gap: 8px;

    &-box {
      width: 3px;
      height: 12px;
      border-radius: 7px;
      background: @primary-color;
    }

    &-line {
      margin-bottom: 25px;
      border-bottom: 1px solid @border-color-base;
    }
  }
</style>
