<template>
  <div>
    <div
      ref="uploadimageRefEl"
      :class="{
        'UploadImage-disabled': disabled,
        horizontal: layout === 'horizontal',
        vertical: layout === 'vertical',
      }"
      class="UploadImage-box"
    >
      <div
        class="UploadImage-item UploadImage-nodrag"
        :class="{
          horizontal: layout === 'horizontal',
          vertical: layout === 'vertical',
        }"
      >
        <div class="UploadImage-border" :id="boxId">
          <div
            v-show="uploadStatus === 'normal'"
            class="UploadImage-upload"
            :style="{
              'pointer-events': disabled ? 'none' : 'auto',
            }"
            @click="method.handeUpload"
          >
            <cloud-upload-outlined class="UploadImage-upload-icon" />
            <p>{{ t('component.upload_image.default_text') }}</p>
            <p> {{ t('component.upload_image.desc_text', [showAccept]) }}</p>
            <p>{{ t('component.upload_image.maxSize', ['2MB']) }}</p>
          </div>

          <div v-show="uploadStatus === 'uploading'" class="UploadImage-progress">
            <Progress :percent="progress" status="active" />
          </div>
        </div>
      </div>

      <div
        class="UploadImage-item"
        v-for="(item, index) in fileList"
        :key="item.url"
        :data-key="item.url"
        :class="{
          horizontal: layout === 'horizontal',
          vertical: layout === 'vertical',
        }"
      >
        <div class="UploadImage-imbox UploadImage-border">
          <Image :src="item?.url" class="UploadImage-imbox-img" height="100%" width="100%" />
          <div class="UploadImage-imbox-buttom" @click="method.handleDelete(index)">
            <CloseOutlined />
          </div>
        </div>
      </div>
    </div>

    <input
      ref="fileInput"
      type="file"
      @change="onFileChange"
      class="UploadImage-input"
      :accept="acceptJoin"
    />
  </div>
</template>
<script setup lang="ts">
  import { ref, unref, toRefs, computed, onMounted, useTemplateRef, onUnmounted } from 'vue';
  import { CloudUploadOutlined, CloseOutlined } from '@ant-design/icons-vue';
  import { Progress, Image, message } from 'ant-design-vue';
  import { uuid } from '@/utils/uuid';
  import { apiUploadFile } from '@/api/admin/file';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { FileModel, ResModel } from './types/UploadImage';
  import { sleep } from '@/utils/other';
  import { PhotoCompress } from '@/utils/photoCompress';
  import { useI18n } from '@/hooks/web/useI18n';
  import { filter, map, flatten, get } from 'lodash-es';
  import Sortable from 'sortablejs';

  defineOptions({ name: 'UploadImage' });

  const { t } = useI18n();
  const uploadimageRef = useTemplateRef('uploadimageRefEl');

  type ValueState = string;

  type Accept =
    | 'image/jpeg'
    | 'image/png'
    | 'image/jpg'
    | 'image/vnd.microsoft.icon'
    | 'image/x-icon';
  type FileType = 'JPEG' | 'PNG' | 'JPG' | 'ICO';
  interface AcceptMap {
    name: FileType;
    value: Accept[];
  }
  interface Props {
    value?: ValueState;
    count?: number;
    disabled?: boolean;
    fileType?: FileType[];
    layout?: 'vertical' | 'horizontal';
  }
  const props = withDefaults(defineProps<Props>(), {
    value: undefined,
    count: 1,
    disabled: false,
    fileType: () => ['JPEG', 'JPG', 'PNG'],

    layout: 'horizontal',
  });
  // 允许图片上传的图片类型
  const acceptMap: AcceptMap[] = [
    {
      name: 'JPEG',
      value: ['image/jpeg'],
    },
    {
      name: 'PNG',
      value: ['image/png'],
    },
    {
      name: 'JPG',
      value: ['image/jpg'],
    },
    {
      name: 'ICO',
      value: ['image/vnd.microsoft.icon', 'image/x-icon'],
    },
  ];
  defineEmits(['change', 'update:value']);
  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', 'change');

  const acceptList = computed(() => {
    let result = map(
      filter(acceptMap, (item) => props.fileType.includes(item.name)),
      'value',
    );
    return flatten(result);
  });
  const acceptJoin = computed(() => {
    if (acceptList.value?.length > 0) {
      return acceptList.value.join(',');
    } else {
      return '';
    }
  });
  const showAccept = computed(() => {
    if (props.fileType?.length > 0) {
      return props.fileType.join('、');
    } else {
      return '';
    }
  });

  const boxId = ref(uuid());

  // const dropActive = ref(false);
  // const dropAreaEvent = {} as any;
  const fileInput = ref<any>();
  const progress = ref(0);
  const uploadStatus = ref('normal'); // normal | uploading | success | error

  const { value, disabled } = toRefs(props);

  const photoCompress = new PhotoCompress(1024 * 1024 * 0.5, 1024 * 1024 * 2);
  const fileList = computed({
    get(): FileModel[] {
      let _modelValue = unref(value);
      if (_modelValue) {
        let arr = _modelValue.split(',').map((item) => {
          const itemUrlLs = item.split('/');
          itemUrlLs.reverse();
          return {
            url: item,
            fileName: itemUrlLs[0],
            bucketName: itemUrlLs[1],
          };
        });
        return arr;
      } else {
        return [];
      }
    },
    set(val: any) {
      state.value = val.join(',');
    },
  });
  // 文件名验证方法
  function fileValidator(file) {
    const { name, type } = file;
    if (/,/.test(name)) {
      message.warn('文件名不能包含逗号');
      return false;
    }
    console.log(type);
    if (!acceptList.value.includes(type)) {
      message.warn(`不支持的文件类型,仅支持 ${showAccept.value} 图片类型`);
      return false;
    }
    return true;
  }

  function onFileChange(e) {
    const files = e.target.files;
    if (files.length > 0) {
      let file = files[0];
      if (!fileValidator(file)) return false;

      if (file.size > 2097152) {
        photoCompress.compress(file, (result) => {
          uploadFile(result);
        });
      } else {
        uploadFile(file);
      }
    } else {
      console.warn('没有文件');
    }
  }

  async function uploadFile(file) {
    console.log('大小', file.size);
    try {
      progress.value = 0;
      uploadStatus.value = 'uploading';
      let res = await apiUploadFile(
        {
          file,
        },
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              progress.value = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
            } else {
              progress.value = 0;
            }
          },
        },
      );
      console.log('上传成功', res);
      const { data, msg, code } = res as ResModel;
      if (code !== 0) {
        message.error(msg);
        return;
      }
      progress.value = 100;
      // const url = `${globSetting.apiUrl}${get(data, 'url', '')}`;
      const url = `${get(data, 'endpoint', '')}/${get(data, 'bucketName', '')}/${get(data, 'fileName', '')}`;
      const urlList = fileList.value.map((item) => item.url);
      if (urlList.length >= unref(props.count)) {
        urlList.shift();
      }
      urlList.push(url);
      await sleep(500);
      fileList.value = urlList;
    } catch (error) {
      message.error('上传失败');
    } finally {
      progress.value = 0;
      fileInput.value.value = '';
      uploadStatus.value = 'normal';
    }
  }

  const method = {
    // 上传
    handeUpload() {
      fileInput.value?.click();
    },
    // 删除
    handleDelete(index) {
      const urlList = fileList.value.map((item) => item.url);
      urlList.splice(index, 1);
      fileList.value = urlList;
    },
  };

  let sortableInstance: Sortable | null = null;
  onMounted(() => {
    const uploadimageEl = uploadimageRef.value;
    if (!uploadimageEl) {
      return;
    }
    sortableInstance = Sortable.create(uploadimageEl, {
      disabled: false,
      animation: 150,
      filter: '.UploadImage-nodrag',

      // 禁止拖动其他任何元素到第一个位置
      onMove(evt) {
        const firstItem = evt.from.querySelector('.UploadImage-nodrag');
        const toItem = evt.related;
        if (toItem !== firstItem) {
          // 禁止将第一个元素拖到其他地方
          return true; // 直接返回 false，阻止拖动
        }
        return false;
      },
      onEnd(evt) {
        // 获取排序后的列表顺序
        let newOrder = Array.from(evt.from.children).map((item) => {
          // 获取 data-key 属性的值
          const key = item.getAttribute('data-key');
          return key;
        });
        // 过滤掉空
        newOrder = newOrder.filter((item) => item);
        fileList.value = newOrder.filter((item) => item);
      },
    });
  });
  onUnmounted(() => {
    if (sortableInstance) {
      sortableInstance.destroy();
    }
  });
</script>

<style lang="less">
  .UploadImage-box {
    width: 100%;
    height: 100%;

    // 元素之间的间距

    // 垂直布局
    &.vertical {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    // 水平布局
    &.horizontal {
      display: flex;
      flex-flow: row wrap;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;
    }
  }

  .UploadImage-disabled {
    // 禁用
    opacity: 0.5;

    & .UploadImage-border {
      &:hover {
        border-color: @border-color-base;
        color: @text-color-base;
      }
    }

    & .UploadImage-imbox-buttom {
      display: none;
    }
  }

  .UploadImage-upload {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    align-items: center;
    justify-content: center;
    color: @text-color-base;

    &-icon {
      font-size: 32px;
    }

    & > p {
      margin: 0;
      margin-top: 10px;
      padding: 0;
      font-size: 12px;
      line-height: 10px;
    }

    &:hover {
      color: @primary-color;
    }
  }

  .UploadImage-progress {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 15px;
    color: @text-color-base;
  }

  .UploadImage-border {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;
    transition: all 0.3s; /* 过渡颜色 */
    border: @border-color-base solid 1px;
    border-radius: 6px;

    &:hover {
      border-color: @primary-color;
    }
  }

  .UploadImage-input {
    display: none !important;
  }

  .UploadImage-item {
    // 禁止复制
    position: relative;
    background-color: @component-background;
    color: @text-color-base;
    cursor: pointer;
    user-select: none;

    // 垂直布局
    &.vertical {
      width: 100%;
      min-width: 200px;
      max-width: 400px;
      height: 160px;
    }

    // 水平布局
    &.horizontal {
      width: 219px;
      height: 147px;
    }
  }

  .UploadImage-imbox {
    &-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      // 禁止复制
      user-select: none;
      // 禁止拖动
      -webkit-user-drag: none;
    }

    &-buttom {
      display: flex;
      position: absolute;
      top: 8px;
      right: 10px;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      transition: all 0.3s;
      border-radius: 50%;
      background-color: @component-background;
      font-size: 12px;

      &:hover {
        //放大
        transform: scale(1.1);
        color: red;
        cursor: pointer;
      }
    }
  }
</style>
