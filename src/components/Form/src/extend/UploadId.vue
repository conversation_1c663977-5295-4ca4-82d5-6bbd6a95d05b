<template>
  <div :class="{ 'UploadId-disabled': disabled }" class="UploadId-box">
    <div class="UploadId-border UploadId-id head">
      <div
        v-show="uploadStatus.head === 'normal'"
        class="UploadId-upload"
        :style="{
          'pointer-events': disabled ? 'none' : 'auto',
        }"
        @click="method.handeUpload('head')"
      >
      </div>
      <div class="UploadId-border-box" v-if="fileList?.head">
        <Image :src="fileList?.head" class="UploadId-border-box-img" height="100%" width="100%" />
        <div class="UploadId-border-box-buttom" @click="method.handleDelete('head')">
          <CloseOutlined />
        </div>
      </div>

      <div v-show="uploadStatus.head === 'uploading'" class="UploadId-progress">
        <Progress :percent="progress.head" status="active" />
      </div>
    </div>
    <div class="UploadId-border UploadId-id tail">
      <div
        v-show="uploadStatus.tail === 'normal'"
        class="UploadId-upload"
        :style="{
          'pointer-events': disabled ? 'none' : 'auto',
        }"
        @click="method.handeUpload('tail')"
      >
      </div>
      <div class="UploadId-border-box" v-if="fileList?.tail">
        <Image :src="fileList?.tail" class="UploadId-border-box-img" height="100%" width="100%" />
        <div class="UploadId-border-box-buttom" @click="method.handleDelete('tail')">
          <CloseOutlined />
        </div>
      </div>
      <div v-show="uploadStatus.tail === 'uploading'" class="UploadId-progress">
        <Progress :percent="progress.tail" status="active" />
      </div>
    </div>

    <input
      ref="fileInputHead"
      type="file"
      @change="onFileChangeHead"
      class="UploadId-input"
      :accept="acceptJoin"
    />
    <input
      ref="fileInputTail"
      type="file"
      @change="onFileChangeTail"
      class="UploadId-input"
      :accept="acceptJoin"
    />
  </div>
</template>
<script setup lang="ts">
  import { ref, unref, toRefs, computed, reactive } from 'vue';
  import { CloseOutlined } from '@ant-design/icons-vue';
  import { Progress, Image, message } from 'ant-design-vue';
  import { apiUpload } from '@/api/sys/file';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { UploadIdFileModel, ResModel } from './types/UploadImage';
  import { sleep } from '@/utils/other';
  import { PhotoCompress } from '@/utils/photoCompress';
  import { filter, map, flatten, get, cloneDeep } from 'lodash-es';
  import { useGlobSetting } from '@/hooks/setting';

  defineOptions({ name: 'UploadId' });

  type ValueState = string[];

  type Accept =
    | 'image/jpeg'
    | 'image/png'
    | 'image/jpg'
    | 'image/vnd.microsoft.icon'
    | 'image/x-icon';
  type FileType = 'JPEG' | 'PNG' | 'JPG' | 'ICO';

  type UploadFileType = 'head' | 'tail';

  interface AcceptMap {
    name: FileType;
    value: Accept[];
  }
  interface Props {
    value?: ValueState;
    disabled?: boolean;
    fileType?: FileType[];
    dir?: string;
  }
  const props = withDefaults(defineProps<Props>(), {
    value: undefined,
    disabled: false,
    fileType: () => ['JPEG', 'JPG', 'PNG'],
    dir: () => {
      const { uploadDir = '' } = useGlobSetting();
      return uploadDir;
    },
  });
  // 允许图片上传的图片类型
  const acceptMap: AcceptMap[] = [
    {
      name: 'JPEG',
      value: ['image/jpeg'],
    },
    {
      name: 'PNG',
      value: ['image/png'],
    },
    {
      name: 'JPG',
      value: ['image/jpg'],
    },
    {
      name: 'ICO',
      value: ['image/vnd.microsoft.icon', 'image/x-icon'],
    },
  ];
  defineEmits(['change', 'update:value']);
  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', 'change');

  const acceptList = computed(() => {
    let result = map(
      filter(acceptMap, (item) => props.fileType.includes(item.name)),
      'value',
    );
    return flatten(result);
  });
  const acceptJoin = computed(() => {
    if (acceptList.value?.length > 0) {
      return acceptList.value.join(',');
    } else {
      return '';
    }
  });
  const showAccept = computed(() => {
    if (props.fileType?.length > 0) {
      return props.fileType.join('、');
    } else {
      return '';
    }
  });

  const fileInputHead = ref<any>();
  const fileInputTail = ref<any>();

  // normal | uploading | success | error
  type UploadStatusType = 'normal' | 'uploading' | 'success' | 'error';
  const uploadStatus = reactive<{
    head: UploadStatusType;
    tail: UploadStatusType;
  }>({
    head: 'normal',
    tail: 'normal',
  });

  const progress = reactive<{
    head: number;
    tail: number;
  }>({
    head: 0,
    tail: 0,
  });

  const { value, disabled } = toRefs(props);

  const photoCompress = new PhotoCompress(1024 * 1024 * 0.5, 1024 * 1024 * 2);
  const fileList = computed({
    get(): UploadIdFileModel {
      let _modelValue = unref(value);
      if (_modelValue) {
        return {
          head: get(_modelValue, 0),
          tail: get(_modelValue, 1),
        };
      } else {
        return {
          head: undefined,
          tail: undefined,
        };
      }
    },
    set(val: any) {
      state.value = [val.head, val.tail];
    },
  });
  // 文件名验证方法
  function fileValidator(file) {
    const { name, type } = file;
    if (/,/.test(name)) {
      message.warn('文件名不能包含逗号');
      return false;
    }
    console.log(type);
    if (!acceptList.value.includes(type)) {
      message.warn(`不支持的文件类型,仅支持 ${showAccept.value} 图片类型`);
      return false;
    }
    return true;
  }

  function onFileChangeHead(e) {
    onFileChange(e, 'head');
  }

  function onFileChangeTail(e) {
    onFileChange(e, 'tail');
  }

  function onFileChange(e, type: UploadFileType) {
    const files = e.target.files;
    if (files.length > 0) {
      let file = files[0];
      if (!fileValidator(file)) return false;

      if (file.size > 2097152) {
        photoCompress.compress(file, (result) => {
          uploadFile(result, type);
        });
      } else {
        uploadFile(file, type);
      }
    } else {
      console.warn('没有文件');
    }
  }

  async function uploadFile(file, type: UploadFileType) {
    console.log('大小', file.size);
    try {
      method.setUploadProgress(type, 0);
      method.setUploadStatus(type, 'uploading');
      let res = await apiUpload(
        {
          file,
          dir: props.dir,
        },
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const _progress = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
              method.setUploadProgress(type, _progress);
            } else {
              method.setUploadProgress(type, 0);
            }
          },
        },
      );
      console.log('上传成功', res);
      const { data, msg, code } = res as ResModel;
      if (code !== 0) {
        message.error(msg);
        return;
      }
      method.setUploadProgress(type, 100);
      const url = `${get(data, 'host', '')}${get(data, 'url', '')}`;
      const fileObj = cloneDeep(fileList.value);
      fileObj[type] = url;
      fileList.value = fileObj;

      await sleep(500);
    } catch (error) {
      message.error('上传失败');
    } finally {
      method.setUploadProgress(type, 0);
      method.handleClear(type);
      method.setUploadStatus(type, 'normal');
    }
  }

  const method = {
    // 上传
    handeUpload(type: UploadFileType) {
      if (type === 'head') {
        fileInputHead.value?.click();
      } else if (type === 'tail') {
        fileInputTail.value?.click();
      }
    },
    // 删除
    handleDelete(type: UploadFileType) {
      const fileObj = cloneDeep(fileList.value);
      fileObj[type] = undefined;
      fileList.value = fileObj;
    },

    // 清除
    handleClear(type: UploadFileType) {
      if (type === 'head') {
        fileInputHead.value.value = '';
      } else if (type === 'tail') {
        fileInputTail.value.value = '';
      }
    },

    // 设置上传状态
    setUploadStatus(type: UploadFileType, status: UploadStatusType) {
      uploadStatus[type] = status;
    },
    // 设置上传进度
    setUploadProgress(type: UploadFileType, _progress: number) {
      progress[type] = _progress;
    },
  };
</script>

<style lang="less">
  .UploadId-id {
    &.head {
      background-image: url('@/assets/images/imageidhead.png');
      background-size: 100% 100%;
    }

    &.tail {
      background-image: url('@/assets/images/imageidtail.png');
      background-size: 100% 100%;
    }
  }

  .UploadId-box {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    transition: all 0.3s;
    // 元素之间的间距
    gap: 26px;
  }

  .UploadId-disabled {
    // 禁用
    opacity: 0.5;

    & .UploadId-border {
      &:hover {
        border-color: #d9d9d9;
        color: #666;
      }
    }

    & .UploadId-border-box-buttom {
      display: none;
    }
  }

  .UploadId-upload {
    display: flex;
    position: absolute;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #666;
    inset: 0;

    &-icon {
      font-size: 32px;
    }

    & > p {
      margin: 0;
      margin-top: 10px;
      padding: 0;
      font-size: 12px;
      line-height: 10px;
    }
  }

  .UploadId-progress {
    display: flex;
    position: absolute;
    z-index: 8;
    flex-direction: column;
    flex-grow: 1;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 0 15px;
    color: #666;
    inset: 0;
  }

  .UploadId-border {
    position: relative;
    width: 219px;
    height: 147px;
    overflow: hidden;
    transition: all 0.3s;
    border: #f0f1f4c5 solid 1px;
    border-radius: 6px;
    background-color: #fff;
    cursor: pointer;
    // 禁止复制
    user-select: none;

    &:hover {
      border-color: @primary-color;
      color: @primary-color;
    }
  }

  .UploadId-border-box {
    position: relative;
    z-index: 6;
    width: 100%;
    height: 100%;
    inset: 0;

    &-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      // 禁止复制
      user-select: none;
      // 禁止拖动
      -webkit-user-drag: none;
    }

    &-buttom {
      display: flex;
      position: absolute;
      z-index: 9;
      top: 8px;
      right: 10px;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      transition: all 0.3s;
      border-radius: 50%;
      background-color: rgb(0 0 0 / 60%);
      color: #fff;
      font-size: 12px;

      &:hover {
        //放大
        transform: scale(1.1);
        color: red;
        cursor: pointer;
      }
    }
  }

  .UploadId-input {
    display: none !important;
  }
</style>
