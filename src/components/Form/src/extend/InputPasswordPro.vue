<template>
  <div>
    <Input.Password
      :size="size"
      :placeholder="placeholder"
      :autocomplete="autocomplete"
      v-model:value="inputValue"
    />
    <div>
      <Progress
        type="line"
        :percent="safeScore.score"
        :steps="5"
        :stroke-color="safeScore.color"
        trailColor="#d8d8d8"
        :strokeWidth="10"
        :showInfo="false"
      />
      <div>
        {{ t('component.input_password_pro.text_password_strength') }}
        <span
          :style="{
            color: safeScore.color,
          }"
          >{{ safeScore.text }}</span
        >
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Input, Progress } from 'ant-design-vue';
  import { computed } from 'vue';
  import { useI18n } from '@/hooks/web/useI18n';

  const { t } = useI18n();
  // 定义一个接口
  interface Props {
    value?: string;
    placeholder?: string;
    size?: 'small' | 'middle' | 'large' | undefined;
    autocomplete?: string;
  }
  const props = withDefaults(defineProps<Props>(), {
    value: '',
    placeholder: '',
    size: undefined,
    autocomplete: 'off',
  });
  const emit = defineEmits(['update:value', 'change']);
  const inputValue = computed({
    get() {
      return props.value;
    },
    set(val) {
      emit('update:value', val);
      emit('change', val);
    },
  });

  // 密码强度提示对象
  const scoreTips = {
    0: {
      text: t('component.input_password_pro.strength_1'),
      color: '#ff4d4f',
    },
    30: {
      text: t('component.input_password_pro.strength_2'),
      color: '#faad14',
    },
    50: {
      text: t('component.input_password_pro.strength_3'),
      color: '#faad14',
    },
    70: {
      text: t('component.input_password_pro.strength_4'),
      color: '#52c41a',
    },
    90: {
      text: t('component.input_password_pro.strength_5'),
      color: '#52c41a',
    },
  };
  // 安全分数
  const safeScore = computed(() => {
    // 要计算的字符串
    const str = props.value;
    let score = 0;

    // 分数计算规则
    // 一位长度 + 2分
    score += str.length * 2;
    // 大写字母 + 10分
    if (/[A-Z]/.test(str)) {
      score += 10;
    }
    // 小写字母 + 10分
    if (/[a-z]/.test(str)) {
      score += 10;
    }
    // 数字 + 10分
    if (/[0-9]/.test(str)) {
      score += 10;
    }
    // 特殊字符 + 15分
    if (/[~!@#$%^&*()_+<>?:"{},./;'[\]]/.test(str)) {
      score += 10;
    }
    // 大小写字母 + 5分
    if (/^(?=.*[A-Z])(?=.*[a-z]).*$/.test(str)) {
      score += 5;
    }
    // 大小写字母 + 数字 + 10分
    if (/^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9]).*$/.test(str)) {
      score += 10;
    }
    // 计算密码强度
    const divisions = method.calculateDivisions(score, scoreTips);
    return {
      score,
      ...divisions,
    };
  });
  // 监听value的变化

  const method = {
    calculateDivisions(score, scoreTips) {
      let result = scoreTips[0];
      for (let tipScore in scoreTips) {
        if (score >= tipScore) {
          result = scoreTips[tipScore];
        } else {
          break;
        }
      }
      return result;
    },
  };
</script>

<style lang="less" scoped>
  ::v-deep(.ant-progress-steps-item) {
    min-width: 50px;
  }
</style>
