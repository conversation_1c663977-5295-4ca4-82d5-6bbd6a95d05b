import { FormProps } from './types/form';

export const DEFAULT_FROM_COL = {
  xs: {
    span: 24,
    offset: 0,
  },
  sm: {
    span: 24,
    offset: 0,
  },
  md: {
    span: 24,
    offset: 0,
  },
  lg: {
    span: 24,
    offset: 0,
  },
  xl: {
    span: 8,
    offset: 0,
  },
  xxl: {
    span: 6,
    offset: 0,
  },
};

export const DEFAULT_FROM_CONFIG: FormProps = {
  baseColProps: { span: 24 },
  layout: 'vertical',
  rowProps: {
    gutter: 20,
  },
  labelWidth: 120,
  showActionButtonGroup: false,
};

// 双列的表单配置
export const DEFAULT_FORM_COL_CONFIG: FormProps = {
  baseColProps: {
    xs: 24, // 小屏幕下占满一行
    sm: 24, // 小屏幕下占满一行
    md: 12, // 中等屏幕下占半行
    lg: 12, // 大屏幕下占半行
    xl: 12, // 超大屏幕下占半行
  },
  layout: 'vertical',
  rowProps: {
    gutter: 20,
  },
  labelWidth: 120,
  showActionButtonGroup: false,
};
