<template>
  <div>
    <Tree
      v-bind="getAttrs"
      :fieldNames
      v-model:checkedKeys="state"
      v-model:expandedKeys="expandedKeys"
      @check="handleCheck"
    >
      <template #[item]="data" v-for="item in Object.keys($slots)">
        <slot :name="item" v-bind="data || {}"></slot>
      </template>
    </Tree>
  </div>
</template>

<script lang="ts" setup>
  import { type Recordable } from '@vben/types';
  import { type PropType, computed, watch, ref, onMounted, unref, useAttrs } from 'vue';
  import { Tree, TreeProps } from 'ant-design-vue';
  import { isFunction } from '@/utils/is';
  import { get, size } from 'lodash-es';
  import { DataNode } from 'ant-design-vue/es/tree';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';

  defineOptions({ name: 'ApiTree' });

  const props = defineProps({
    api: { type: Function as PropType<(arg?: any) => Promise<Recordable<any>>> },
    params: { type: Object },
    immediate: { type: Boolean, default: true },
    resultField: { type: String, default: '' },
    beforeFetch: {
      type: Function as PropType<Fn>,
      default: null,
    },
    afterFetch: {
      type: Function as PropType<Fn>,
      default: null,
    },
    value: {
      type: Array as PropType<TreeProps['selectedKeys']>,
    },
    hookValue: { type: Function as PropType<Fn> },
    fieldNames: {
      type: Object as PropType<TreeProps['fieldNames']>,
    },
    valueField: { type: String, default: 'value' },
  });

  const emit = defineEmits(['options-change', 'change', 'update:value']);

  const attrs = useAttrs();

  const treeData = ref<DataNode[]>([]);
  const isFirstLoaded = ref<Boolean>(false);
  const loading = ref(false);
  const emitData = ref<any[]>([]);
  const expandedKeys = ref<TreeProps['expandedKeys']>([]);

  const [state] = useRuleFormItem<any, any, TreeProps['selectedKeys']>(
    props,
    'value',
    'change',
    emitData,
  );
  const getAttrs = computed(() => {
    console.log('getAttrs', {
      ...(props.api ? { treeData: unref(treeData) } : {}),
      ...attrs,
    });
    return {
      ...(props.api ? { treeData: unref(treeData) } : {}),
      ...attrs,
    };
  });

  watch(
    () => props.params,
    () => {
      !unref(isFirstLoaded) && fetch();
    },
    { deep: true },
  );

  watch(
    () => props.immediate,
    (v) => {
      v && !isFirstLoaded.value && fetch();
    },
  );

  onMounted(() => {
    props.immediate && fetch();
  });

  function handleCheck(value: TreeProps['checkedKeys'], events) {
    const { hookValue } = props;
    if (hookValue && isFunction(hookValue)) {
      state.value =
        hookValue({
          value,
          events,
          treeData: unref(treeData),
          valueField: props.valueField,
        }) || [];
    }
  }

  // 展开函数
  const expand = () => {
    if (size(treeData.value)) {
      // 默认展开所有
      expandedKeys.value = iterator(treeData.value);
    }
  };

  // 迭代器 获取所有子节点的 key
  const iterator = (data: any[]) => {
    const tmp: string[] = [];
    data.forEach((item) => {
      tmp.push(item[props.valueField]);
      if (item.children) {
        tmp.push(...iterator(item.children));
      }
    });
    return tmp;
  };

  async function fetch() {
    let { api, beforeFetch, afterFetch, params, resultField } = props;
    if (!api || !isFunction(api)) return;
    loading.value = true;
    treeData.value = [];
    let res;
    try {
      if (beforeFetch && isFunction(beforeFetch)) {
        params = (await beforeFetch(params)) || params;
      }
      res = await api(params);
      if (afterFetch && isFunction(afterFetch)) {
        res = (await afterFetch(res)) || res;
      }
    } catch (e) {
      console.error(e);
    } finally {
      loading.value = false;
    }
    if (!res) return;
    if (resultField) {
      res = get(res, resultField) || [];
    }
    treeData.value = (res as (Recordable & { key: string | number })[]) || [];
    !isFirstLoaded.value && expand();
    isFirstLoaded.value = true;
    emit('options-change', treeData.value);
  }
</script>
