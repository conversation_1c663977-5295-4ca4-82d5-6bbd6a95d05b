// 字符串转数组
export function strToArr(str: string): Array<any> {
  // 判断是不是数组
  if (Array.isArray(str)) {
    return str;
  }
  if (typeof str === 'string' && str.length > 0) {
    return str.split(',');
  }
  return [];
}

// 数组转字符串
export function arrToStr(arr: Array<any>): string {
  // 判断是不是字符串
  if (typeof arr === 'string') {
    return arr;
  }
  if (Array.isArray(arr) && arr.length > 0) {
    return arr.join(',');
  }
  return '';
}

export function transformRecordByKeys(
  record: Record<string, any>,
  keys: string[],
  direction: 'toArr' | 'toStr',
): Record<string, any> {
  const transform = direction === 'toArr' ? strToArr : arrToStr;

  keys.forEach((key) => {
    if (record[key]) {
      record[key] = transform(record[key]);
    } else if (record[key] === null) {
      record[key] = [];
    }
  });

  return record;
}

export function transformRecord(data: any, direction?: 'toArr' | 'toStr') {
  if (!direction) return data;
  return (direction === 'toArr' ? strToArr : arrToStr)(data);
}
