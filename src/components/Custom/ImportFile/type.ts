import type { Props as BaseProps } from '@/utils/compFn';

export interface ImportFileProps extends BaseProps {
  templateApi?: () => Promise<any>;
  templateUrl?: string; // 直接提供模板下载URL
  templateParams?: Record<string, any>; // 模板下载参数
  templateFilename?: string; // 模板文件名
  importApi?: (
    params: { file: File },
    onUploadProgress?: (progressEvent: any) => void,
  ) => Promise<any>;
  accept?: string;
  maxSize?: number; // MB
}
