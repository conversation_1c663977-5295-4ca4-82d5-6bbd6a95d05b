<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { Modal, Upload, Button, Progress, message } from 'ant-design-vue';
  import { UploadOutlined, DownloadOutlined, InboxOutlined } from '@ant-design/icons-vue';
  import { downloadByData, downBlobFile } from '@/utils/file/download';
  import { ImportFileProps } from './type';

  defineOptions({ name: 'ImportFile' });

  const props = withDefaults(defineProps<ImportFileProps>(), {
    title: '导入文件',
    accept: '.xlsx,.xls',
    maxSize: 10,
  });

  // const { t } = useI18n(); // 暂时不需要国际化
  const visible = ref(true);
  const loading = ref(false);
  const uploadProgress = ref(0);
  const dragOver = ref(false);
  const selectedFile = ref<File | null>(null);

  // 文件大小限制检查
  const fileSizeLimit = computed(() => props.maxSize * 1024 * 1024);

  // 处理文件选择
  const handleFileSelect = (file: File) => {
    if (file.size > fileSizeLimit.value) {
      message.error(`文件大小不能超过 ${props.maxSize}MB`);
      return false;
    }
    selectedFile.value = file;
    return false; // 阻止自动上传
  };

  // 处理拖拽
  const handleDragOver = (e: DragEvent) => {
    e.preventDefault();
    dragOver.value = true;
  };

  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault();
    dragOver.value = false;
  };

  const handleDrop = (e: DragEvent) => {
    e.preventDefault();
    dragOver.value = false;

    const files = e.dataTransfer?.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (props.accept) {
        const acceptTypes = props.accept.split(',').map((type) => type.trim());
        const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
        if (!acceptTypes.includes(fileExt)) {
          message.error(`只支持 ${props.accept} 格式的文件`);
          return;
        }
      }
      handleFileSelect(file);
    }
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    if (!props.templateApi && !props.templateUrl) {
      message.warning('未配置模板下载接口或URL');
      return;
    }

    try {
      loading.value = true;

      // 优先使用 templateUrl + downBlobFile
      if (props.templateUrl) {
        await downBlobFile(
          props.templateUrl,
          props.templateParams,
          props.templateFilename || '导入模板.xlsx',
        );
      } else if (props.templateApi) {
        // 使用 templateApi
        const response = await props.templateApi();

        // 处理不同类型的响应
        if (response instanceof Blob) {
          downloadByData(response, props.templateFilename || '导入模板.xlsx');
        } else if (response.data instanceof Blob) {
          downloadByData(response.data, props.templateFilename || '导入模板.xlsx');
        } else if (response.url) {
          // 如果返回的是下载链接
          window.open(response.url, '_blank');
        } else {
          message.error('模板下载失败：响应格式不正确');
          return;
        }
      }

      message.success('模板下载成功');
    } catch (error) {
      console.error('模板下载失败:', error);
      message.error('模板下载失败');
    } finally {
      loading.value = false;
    }
  };

  // 执行导入
  const handleImport = async () => {
    if (!selectedFile.value) {
      message.warning('请先选择要导入的文件');
      return;
    }

    if (!props.importApi) {
      message.warning('未配置导入接口');
      return;
    }

    try {
      loading.value = true;
      uploadProgress.value = 0;

      const result = await props.importApi(
        {
          file: selectedFile.value,
        },
        (progressEvent) => {
          // 真实的上传进度回调
          if (progressEvent.total) {
            uploadProgress.value = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
          } else {
            uploadProgress.value = 0;
          }
        },
      );

      uploadProgress.value = 100;

      message.success('导入成功');
      props.onSuccess?.(result);

      // 延迟关闭，让用户看到成功状态
      setTimeout(() => {
        handleClose();
      }, 1000);
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败');
      props.onError?.(error);
      uploadProgress.value = 0;
    } finally {
      loading.value = false;
    }
  };

  // 关闭弹窗
  const handleClose = () => {
    visible.value = false;
    props.close?.();
    handleAfterClose();
  };

  // 弹窗关闭后的回调
  const handleAfterClose = () => {
    console.log('ImportFile closed');
    props.destroy?.();
  };

  // 重置文件选择
  const handleReset = () => {
    selectedFile.value = null;
    uploadProgress.value = 0;
  };

  // 暴露方法给函数式调用
  const beforeClose = () => {
    // 可以在这里添加关闭前的逻辑
  };

  defineExpose({
    beforeClose,
  });
</script>

<template>
  <Modal
    v-model:open="visible"
    :title="title"
    width="600px"
    :confirmLoading="loading"
    :maskClosable="false"
    :keyboard="false"
    @ok="handleImport"
    @cancel="handleClose"
    destroyOnClose
  >
    <div class="import-file-container">
      <!-- 模板下载区域 -->
      <div class="template-section">
        <div class="section-title">
          <DownloadOutlined />
          <span>模板下载</span>
        </div>
        <div class="section-content">
          <p class="tip-text">请先下载导入模板，按照模板格式填写数据后再进行导入</p>
          <Button type="primary" :loading="loading" @click="handleDownloadTemplate">
            <DownloadOutlined />
            下载模板
          </Button>
        </div>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <div class="section-title">
          <UploadOutlined />
          <span>文件导入</span>
        </div>
        <div class="section-content">
          <div
            class="drag-upload-area"
            :class="{ 'drag-over': dragOver, 'has-file': selectedFile }"
            @dragover="handleDragOver"
            @dragleave="handleDragLeave"
            @drop="handleDrop"
          >
            <Upload
              :accept="accept"
              :beforeUpload="handleFileSelect"
              :showUploadList="false"
              :disabled="loading"
            >
              <div class="upload-content">
                <div class="upload-icon">
                  <InboxOutlined />
                </div>
                <div class="upload-text">
                  <p class="primary-text">
                    {{ selectedFile ? selectedFile.name : '点击或拖拽文件到此区域上传' }}
                  </p>
                  <p class="secondary-text">
                    支持 {{ accept }} 格式，文件大小不超过 {{ maxSize }}MB
                  </p>
                </div>
              </div>
            </Upload>
          </div>

          <!-- 文件信息和操作 -->
          <div v-if="selectedFile" class="file-info">
            <div class="file-details">
              <span class="file-name">{{ selectedFile.name }}</span>
              <span class="file-size">{{ (selectedFile.size / 1024 / 1024).toFixed(2) }}MB</span>
            </div>
            <Button size="small" @click="handleReset">重新选择</Button>
          </div>

          <!-- 上传进度 -->
          <div v-if="loading && uploadProgress > 0" class="progress-section">
            <Progress
              :percent="uploadProgress"
              :showInfo="true"
              :strokeColor="uploadProgress === 100 ? '#52c41a' : '#1890ff'"
            />
            <p class="progress-text">
              {{ uploadProgress === 100 ? '导入完成' : '正在导入...' }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <Button @click="handleClose" :disabled="loading">取消</Button>
      <Button type="primary" @click="handleImport" :loading="loading" :disabled="!selectedFile">
        {{ loading ? '导入中...' : '开始导入' }}
      </Button>
    </template>
  </Modal>
</template>

<style lang="less" scoped>
  .import-file-container {
    .template-section,
    .upload-section {
      margin-bottom: 24px;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        color: #262626;
        font-size: 16px;
        font-weight: 500;
        gap: 8px;
      }

      .section-content {
        padding: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        background: #fafafa;
      }
    }

    .template-section {
      .tip-text {
        margin-bottom: 12px;
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .drag-upload-area {
      transition: all 0.3s;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background: #fff;
      cursor: pointer;

      &:hover,
      &.drag-over {
        border-color: #1890ff;
        background: #f6ffed;
      }

      &.has-file {
        border-color: #52c41a;
        background: #f6ffed;
      }

      // 确保 Upload 组件撑满容器
      :deep(.ant-upload) {
        display: block !important;
        width: 100% !important;
        height: 100% !important;
      }

      // 确保 Upload 内部容器撑满
      :deep(.ant-upload-drag) {
        height: 100% !important;
        padding: 0 !important;
        border: none !important;
        background: transparent !important;
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        min-height: 120px;
        padding: 32px 16px;
        text-align: center;

        .upload-icon {
          margin-bottom: 16px;
          color: #d9d9d9;
          font-size: 48px;
        }

        .upload-text {
          .primary-text {
            margin-bottom: 8px;
            color: #262626;
            font-size: 16px;
          }

          .secondary-text {
            margin: 0;
            color: #8c8c8c;
            font-size: 14px;
          }
        }
      }
    }

    .file-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;
      padding: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      background: #fff;

      .file-details {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .file-name {
          color: #262626;
          font-weight: 500;
        }

        .file-size {
          color: #8c8c8c;
          font-size: 12px;
        }
      }
    }

    .progress-section {
      margin-top: 16px;

      .progress-text {
        margin-top: 8px;
        color: #666;
        font-size: 14px;
        text-align: center;
      }
    }
  }
</style>
