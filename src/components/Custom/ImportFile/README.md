# ImportFile 导入组件

一个功能完整的文件导入组件，支持拖拽上传、模板下载、进度显示等功能，采用函数式调用方式。

## 功能特性

- ✅ 拖拽上传支持
- ✅ 点击选择文件
- ✅ 模板下载功能
- ✅ 文件格式验证
- ✅ 文件大小限制
- ✅ 上传进度显示
- ✅ 函数式调用
- ✅ 自定义配置
- ✅ 成功/失败回调
- ✅ 关闭动画支持
- ✅ 自动动画检测

## 基本用法

```typescript
import { createImportFile } from '@/components/Custom/ImportFile';

// 基本使用
const { close } = createImportFile({
  title: '导入用户数据',
  templateApi: () => apiDownloadUserTemplate(),
  importApi: (file) => apiImportUsers(file),
  onSuccess: (result) => {
    console.log('导入成功:', result);
    // 刷新列表等操作
  },
  onError: (error) => {
    console.error('导入失败:', error);
  },
});
```

## API 参数

### ImportFileOptions

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| title | `string` | `'导入文件'` | 弹窗标题 |
| templateApi | `() => Promise<any>` | - | 模板下载 API |
| templateUrl | `string` | - | 模板下载 URL（使用 downBlobFile） |
| templateParams | `Record<string, any>` | - | 模板下载参数 |
| templateFilename | `string` | - | 模板文件名 |
| importApi | `(params: { file: File }, onUploadProgress?: (progressEvent: any) => void) => Promise<any>` | - | 文件导入 API，支持进度回调 |
| accept | `string` | `'.xlsx,.xls'` | 允许的文件格式 |
| maxSize | `number` | `10` | 最大文件大小(MB) |
| onSuccess | `(result: any) => void` | - | 导入成功回调 |
| onError | `(error: any) => void` | - | 导入失败回调 |

### 返回值

```typescript
{
  close: () => Promise<void>; // 手动关闭弹窗
}
```

## 使用示例

### 1. 基本导入

```typescript
import { createImportFile } from '@/components/Custom/ImportFile';

const handleImport = () => {
  createImportFile({
    title: '导入数据',
    templateApi: async () => {
      // 返回 Blob 对象
      const response = await fetch('/api/template/download');
      return response.blob();
    },
    importApi: async (params, onUploadProgress) => {
      const formData = new FormData();
      formData.append('file', params.file);

      // 使用 XMLHttpRequest 以支持进度回调
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        if (onUploadProgress) {
          xhr.upload.addEventListener('progress', onUploadProgress);
        }

        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(JSON.parse(xhr.responseText));
          } else {
            reject(new Error('Upload failed'));
          }
        };

        xhr.onerror = () => reject(new Error('Upload failed'));
        xhr.open('POST', '/api/import');
        xhr.send(formData);
      });
    },
    onSuccess: (result) => {
      message.success('导入成功');
      // 刷新数据
    },
  });
};
```

### 2. 自定义配置

```typescript
createImportFile({
  title: '批量导入商品',
  accept: '.xlsx,.xls,.csv',
  maxSize: 20, // 20MB
  templateApi: () => apiDownloadProductTemplate(),
  importApi: (params, onUploadProgress) => apiImportProducts(params, { onUploadProgress }),
  onSuccess: (result) => {
    console.log(`导入完成：成功 ${result.success} 条，失败 ${result.failed} 条`);
  },
  onError: (error) => {
    console.error('导入失败:', error);
  },
});
```

### 3. 处理不同的模板 API 响应

```typescript
// 模板 API 可以返回不同格式的数据
const templateApi = async () => {
  const response = await apiDownloadTemplate();

  // 情况1: 直接返回 Blob
  if (response instanceof Blob) {
    return response;
  }

  // 情况2: 响应包含 data 字段的 Blob
  if (response.data instanceof Blob) {
    return response;
  }

  // 情况3: 返回下载链接
  if (response.url) {
    return { url: response.url };
  }

  return response;
};
```

### 4. 使用 downBlobFile 下载模板

```typescript
// 使用 downBlobFile 函数下载模板
createImportFile({
  title: '导入数据',
  // 使用 templateUrl 会自动调用 downBlobFile
  templateUrl: '/admin/dict/export',
  templateParams: {
    type: 'user_template',
    format: 'xlsx',
  },
  templateFilename: '用户导入模板.xlsx',
  importApi: (params, onUploadProgress) => apiImportUsers(params, { onUploadProgress }),
  onSuccess: (result) => {
    message.success('导入成功');
  },
});
```

### 5. 完整的业务场景

```typescript
// 用户管理页面的导入功能
const handleUserImport = () => {
  createImportFile({
    title: '批量导入用户',
    accept: '.xlsx,.xls',
    maxSize: 5,
    templateApi: () => apiDownloadUserTemplate(),
    importApi: async (params, onUploadProgress) => {
      // 使用项目中的上传 API
      return apiImportUsers(params, { onUploadProgress });
    },
    onSuccess: (result) => {
      const { total, success, failed } = result.data;
      message.success(`导入完成！总计：${total}，成功：${success}，失败：${failed}`);

      // 刷新用户列表
      reload();
    },
    onError: (error) => {
      message.error('导入失败，请检查文件格式');
      console.error(error);
    },
  });
};
```

## 注意事项

1. **模板 API**: 需要返回可下载的文件数据（Blob）或下载链接
2. **导入 API**: 接收 File 对象作为参数，返回导入结果
3. **文件格式**: 通过 `accept` 参数控制允许的文件类型
4. **文件大小**: 通过 `maxSize` 参数控制最大文件大小（单位：MB）
5. **错误处理**: 建议在 `onError` 回调中处理错误情况

## 样式自定义

组件使用了 Less 样式，可以通过覆盖 CSS 类来自定义样式：

```less
// 自定义拖拽区域样式
.drag-upload-area {
  &.drag-over {
    border-color: #your-color !important;
    background: #your-background !important;
  }
}

// 自定义进度条颜色
.progress-section {
  .ant-progress-bg {
    background: #your-color !important;
  }
}
```

## 技术实现

- 基于 `createFuncComp` 工具函数实现函数式调用
- 使用 Ant Design Vue 的 Modal、Upload、Progress 等组件
- 支持原生拖拽 API
- 集成项目的下载工具函数 `downloadByData`
