<script lang="ts" setup>
  import { Tree } from 'ant-design-vue';
  import { ref, computed, watch, useAttrs, h } from 'vue';
  import { useSafeVModel } from '@/hooks/component/useSafeVModel';
  import { LoadingOutlined } from '@ant-design/icons-vue';
  import BasicSelectTreeItem from './BasicSelectTreeItem.vue';
  import type { BasicSelectTreeProps, TreeKey } from './types/basicSelectTree';

  const indicator = h(LoadingOutlined, {
    style: {
      fontSize: '24px',
    },
    spin: true,
  });

  const props = withDefaults(defineProps<BasicSelectTreeProps>(), {
    treeData: () => [],
    labelField: 'title',
    valueField: 'key',
    childrenField: 'children',
    selectedKey: undefined,
    loading: false,
  });

  defineEmits<{
    'update:selectedKey': [TreeKey];
  }>();

  /* -------------------------------------------------- */
  /*  外部 v-model（可选）                               */
  /* -------------------------------------------------- */
  const modelSelectedKey = useSafeVModel(props, 'selectedKey');

  /* -------------------------------------------------- */
  /*  树数据、字段映射                                   */
  /* -------------------------------------------------- */

  const actualTreeData = computed(() => (props.treeData.length ? props.treeData : []));

  const fieldNames = {
    title: props.labelField,
    key: props.valueField,
    children: props.childrenField,
  };

  /* -------------------------------------------------- */
  /*  祖先映射 & 自动展开逻辑                            */
  /* -------------------------------------------------- */
  /** <选中节点 key, 其父节点 key> 映射 */
  const parentMap = computed(() => {
    const map = new Map<TreeKey, TreeKey>();
    const traverse = (nodes: any[], parent: TreeKey | null) => {
      nodes.forEach((n) => {
        const k = n[props.valueField];
        if (parent != null) map.set(k, parent);
        const children = n[props.childrenField];
        if (Array.isArray(children) && children.length) traverse(children, k);
      });
    };
    traverse(actualTreeData.value, null);
    return map;
  });

  /** 递归获取一个节点的全部祖先 key */
  const getAncestorKeys = (key: TreeKey | null): TreeKey[] => {
    const res: TreeKey[] = [];
    let cur = key;
    while (cur != null) {
      const p = parentMap.value.get(cur);
      if (p != null) res.push(p);
      cur = p ?? null;
    }
    return res;
  };

  /* 当前需要展开的 key 列表（受控） */
  const expandedKeys = ref<TreeKey[]>([]);

  /* 当选中项或树数据变化 -> 重新计算展开项 */
  const updateExpandBySelect = (k: TreeKey | null) => {
    if (k == null) return;

    // 展开路径上的所有祖先，再加上用户已展开的其他节点
    const needExpand = Array.from(new Set([...expandedKeys.value, ...getAncestorKeys(k)]));
    expandedKeys.value = needExpand;
  };
  /* -------------------------------------------------- */
  /*  初始化默认选中第一节点                             */
  /* -------------------------------------------------- */
  const getFirstSelectableKey = (data: any[]): TreeKey | null => {
    for (const n of data) {
      const key = n[props.valueField];
      if (key != null) return key;
      const children = n[props.childrenField];
      if (Array.isArray(children)) {
        const result = getFirstSelectableKey(children);
        if (result != null) return result;
      }
    }
    return null;
  };
  // 默认选择项 将函数提取出来 不在 onMounted中调用
  const getDefaultSelectedKey = () => {
    if (modelSelectedKey.value == null) {
      const first = getFirstSelectableKey(actualTreeData.value);
      if (first) handleSelect([first]);
    }
  };
  watch(modelSelectedKey, (k) => updateExpandBySelect(k));
  watch(
    actualTreeData,
    () => {
      getDefaultSelectedKey();
      updateExpandBySelect(modelSelectedKey.value);
    },
    {
      immediate: true,
    },
  );

  /* 用户点击 ± 折叠/展开 */
  function handleExpand(keys: TreeKey[]) {
    expandedKeys.value = keys;
  }

  /* -------------------------------------------------- */
  /*  选中逻辑                                           */
  /* -------------------------------------------------- */
  function handleSelect(keys: TreeKey[]) {
    if (!keys.length) return; // 禁止取消
    const k = keys[0];
    modelSelectedKey.value = k; // 刷新视图
  }

  /* Tree 受控 selectedKeys */
  const treeSelectedKeys = computed<TreeKey[] | undefined>(() =>
    modelSelectedKey.value != null ? [modelSelectedKey.value] : undefined,
  );

  /* 透传其它属性 */
  const attrs = useAttrs();
</script>

<template>
  <a-spin
    :indicator="indicator"
    :spinning="loading"
    class="zui-common-antd-spin-class"
    wrapperClassName="zui-common-antd-spin-wrapper"
  >
    <Tree
      v-bind="attrs"
      :field-names="fieldNames"
      :tree-data="actualTreeData"
      :selected-keys="treeSelectedKeys"
      :expanded-keys="expandedKeys"
      @select="handleSelect"
      @expand="handleExpand"
      autoExpandParent
      :showLine="{ showLeafIcon: false }"
    >
      <template #title="{ selected, dataRef, level, children }">
        <slot
          name="title"
          :title="dataRef[props.labelField]"
          :selected="selected"
          :record="dataRef"
          :level="level"
          :children="children"
        >
          <BasicSelectTreeItem
            :title="dataRef[props.labelField]"
            :selected="selected"
            :record="dataRef"
            :level="level"
            :children="children"
            :actions="actions"
          />
        </slot>
      </template>
    </Tree>
    <!-- // 空数据展示 -->
    <a-empty
      class="h-full flex justify-center items-center flex-col"
      v-if="actualTreeData.length === 0"
      description="暂无数据"
    />
  </a-spin>
</template>
<style lang="less" scoped>
  /* 根据需要添加样式 */

  /* 穿透样式 */
  :deep(.ant-tree-treenode) {
    flex-shrink: 0;
    width: 100%;
  }

  :deep(.ant-tree-node-content-wrapper) {
    flex-grow: 1;
  }

  :deep(.ant-tree-treenode-motion) {
    width: 100%;
  }

  :deep(.ant-tree-switcher) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
