<template>
  <div class="flex flex-wrap gap-4">
    <div
      v-for="option in options"
      :key="option.value"
      class="button px-4 py-1.5 text-sm rounded cursor-pointer transition-all hover:bg-[rgba(56,128,115,0.14)]"
      :class="{
        'bg-[rgba(56,128,115,0.10)] dark:bg-#388073 text-#388073 dark:text-#fff':
          value === option.value,
        'bg-[--border-color] text-[--text-color]': value !== option.value,
      }"
      @click="change(option.value)"
    >
      {{ option.label }}
    </div>
  </div>
</template>

<script setup lang="tsx">
  import { cloneDeep } from 'lodash-es';

  const value = defineModel<string | number>('value');
  const { options = [] } = defineProps<{
    options: { value: string | number; label: string | number }[];
  }>();
  const emits = defineEmits<{
    /**
     * 变更回调
     * @param value 变更后的值
     */
    change: [value: string | number];
  }>();

  /**
   * ====================
   *       基本逻辑
   * ====================
   */
  /**
   * 更新值
   * @param val 值
   */
  function change(val: string | number) {
    value.value = cloneDeep(val);
    emits('change', cloneDeep(val));
  }
</script>

<style lang="less" scoped></style>
