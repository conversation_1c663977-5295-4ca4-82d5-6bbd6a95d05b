import type { MenuItemProps } from 'ant-design-vue';
/* -------------------------------------------------- */
/*  基础类型、Props、Emit                              */
/* -------------------------------------------------- */
export type TreeKey = string | number;

export interface TreeActionItem extends MenuItemProps {
  label: string;
  key?: string;
  ifShow?: boolean | ((data: any) => boolean);
  onClick?: (data: any) => void;
}

export interface BasicSelectTreeProps {
  treeData?: any[];
  labelField?: string;
  valueField?: string;
  childrenField?: string;
  /** 外部单选绑定 */
  selectedKey?: TreeKey;
  loading?: boolean;
  /**按钮渲染列表  */
  actions?: TreeActionItem[];
}
