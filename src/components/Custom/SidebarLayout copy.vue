<script setup lang="ts">
  import { ref, computed, provide } from 'vue';
  import { useMediaQuery, useToggle, useEventListener } from '@vueuse/core';

  /* -------------- 配置 -------------- */
  const props = withDefaults(
    defineProps<{
      /** 小于该宽度视为移动端，自动折叠 */
      mobileBreakpoint?: number;
      /** 默认侧栏宽度（px） */
      defaultWidth?: number;
      /** 侧栏最小可见宽度（px），再小就折叠 */
      minVisibleWidth?: number;
      /** 折叠时仍保留的拖拽手柄宽度（px） */
      collapsedHandleWidth?: number;
    }>(),
    {
      mobileBreakpoint: 768,
      defaultWidth: 256,
      minVisibleWidth: 120,
      collapsedHandleWidth: 4,
    },
  );

  /* -------------- 状态 -------------- */
  const isWide = useMediaQuery(`(min-width: ${props.mobileBreakpoint}px)`);
  const [userCollapsed, toggleCollapsed] = useToggle(false);
  const sidebarWidth = ref(props.defaultWidth);

  /* 折叠前最后一次的宽度，方便恢复时还原 */
  let lastWidth = props.defaultWidth;

  /* -------------- 拖拽 -------------- */
  const layoutRef = ref<HTMLElement | null>(null);
  const isDragging = ref(false);
  let layoutLeft = 0; // 容器左边缘在视口中的 x

  function onHandleDown(e: PointerEvent) {
    if (!isWide.value) return; // 移动端不允许拖
    // 捕获指针，避免拖拽过快导致 pointerup 丢失
    (e.target as HTMLElement).setPointerCapture(e.pointerId);

    isDragging.value = true;
    layoutLeft = layoutRef.value?.getBoundingClientRect().left ?? 0;
    e.preventDefault();
  }

  useEventListener(window, 'pointermove', (e: PointerEvent) => {
    if (!isDragging.value) return;

    /* 计算新的宽度（相对于容器左边缘） */
    const newWidth = Math.round(e.clientX - layoutLeft);

    if (newWidth < props.minVisibleWidth) {
      // 进入折叠态
      if (!userCollapsed.value) lastWidth = sidebarWidth.value;
      userCollapsed.value = true;
    } else {
      userCollapsed.value = false;
      sidebarWidth.value = newWidth;
    }
  });

  useEventListener(window, 'pointerup', () => {
    isDragging.value = false;
  });

  /* -------------- 可见性 -------------- */
  const sidebarVisible = computed(() => isWide.value && !userCollapsed.value);

  /* -------------- provide / 插槽上下文 -------------- */
  const ctx = {
    sidebarVisible,
    toggleSidebar: () => {
      if (!isWide.value) return;
      if (userCollapsed.value) {
        // 从折叠 → 展开：恢复之前宽度，若没有记录则用默认宽度
        sidebarWidth.value = lastWidth || props.defaultWidth;
      }
      toggleCollapsed();
    },
  };
  provide('sidebarCtx', ctx);
</script>

<template>
  <div ref="layoutRef" class="flex w-full h-full relative select-none">
    <!-- 左侧栏 -->
    <div
      v-if="sidebarVisible"
      class="shrink-0 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
      :style="{ width: `${sidebarWidth}px` }"
    >
      <slot name="left" v-bind="ctx"></slot>
    </div>

    <!-- 始终存在的拖拽手柄：折叠时宽度为 collapsedHandleWidth -->
    <div
      class="drag-handle cursor-col-resize transition-colors"
      :class="{ collapsed: !sidebarVisible }"
      :style="{
        width: sidebarVisible ? '4px' : `${props.collapsedHandleWidth}px`,
      }"
      @pointerdown="onHandleDown"
    ></div>

    <!-- 右侧主区域 -->
    <div class="flex-1 overflow-auto">
      <slot name="right" v-bind="ctx"></slot>
    </div>
  </div>
</template>

<style scoped>
  .drag-handle {
    /* 默认窄分割线 */
    background: transparent;
  }

  .drag-handle:hover {
    background: rgb(59 130 246 / 30%); /* Tailwind 的 blue-500/30 */
  }

  /* 折叠态：手柄占位更宽，但默认透明；悬停才有色 */
  .drag-handle.collapsed {
    background: transparent;
  }

  .drag-handle.collapsed:hover {
    background: rgb(59 130 246 / 30%);
  }
</style>
