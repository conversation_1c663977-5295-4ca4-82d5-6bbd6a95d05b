<!-- AnimatedTabs.vue -->
<template>
  <!-- ★ 外层容器的高度由父级决定。此处只需写 h-full 接收即可 -->
  <div class="animated-tabs flex flex-col h-full">
    <!-- header -------------------------------------------------------- -->
    <div v-if="$slots.actions" class="tabs-actions mb-2 shrink-0">
      <slot name="actions" :active-code="modelValue"></slot>
    </div>

    <!-- content（可滚动）--------------------------------------------- -->
    <div class="tabs-content flex-1 min-h-0 overflow-auto">
      <ul class="flex flex-col gap-1 p-1 pt-2">
        <li
          v-for="item in list"
          :key="item[keyField]"
          :class="[
            // ① 选中状态
            isActive(item)
              ? 'bg-[#FBF3ED] AnimatedTabs-color  before:absolute before:left-0 before:top-0 before:bottom-0 before:w-[3px]  '
              : // ② 未选中 + 悬停
                'hover:bg-gray-100 AnimatedTabs-hover',
            // 通用样式
            'relative cursor-pointer px-[4px] py-[10px] rounded-[4px] transition-colors flex items-center gap-[4px] ',
          ]"
          @click="onSelect(item[keyField], item)"
          tabindex="0"
        >
          <!-- Motion 高亮背景（用同一激活色）-->
          <motion.div
            v-if="isActive(item)"
            layout
            layout-id="active-tab-bg"
            class="absolute inset-0 z-1 bg-[#FBF3ED] rounded-[4px]"
          />
          <div class="relative z-2 w-full flex items-center gap-[4px]">
            <slot name="item" :item="item" :active="isActive(item)">
              <span :class="getIconClass(item[keyField])"></span>
              <span>{{ item[titleField] }}</span>
              <span v-if="item.count !== undefined"> ({{ item.count }}) </span>
            </slot>
          </div>
        </li>
      </ul>
    </div>

    <!-- footer -------------------------------------------------------- -->
    <div v-if="$slots.footer" class="tabs-footer mt-2 shrink-0">
      <slot name="footer" :active-code="modelValue"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { watch, computed } from 'vue';
  import { motion } from 'motion-v';
  import { useVModels } from '@vueuse/core';

  interface Props {
    list: any[];
    modelValue?: any;
    // 对应 key在list 中的的数据
    modelItem?: any;
    fieldNames?: { key: string; title: string };
    iconMap?: Record<string, string> | ((code: string) => string);
  }

  const props = withDefaults(defineProps<Props>(), {
    list: () => [],
    fieldNames: () => ({ key: 'id', title: 'name' }),
  });

  const emit = defineEmits<{
    (e: 'update:modelValue', v: any): void;
    (e: 'update:modelItem', v: any): void;
    (e: 'select', v: any, item?: any): void;
  }>();

  const { modelValue, modelItem } = useVModels(props, emit);

  /* ---------------- 状态 ---------------- */
  const keyField = computed(() => props.fieldNames.key);
  const titleField = computed(() => props.fieldNames.title);

  watch(
    () => props.list,
    (list) => {
      if (!modelValue.value && list?.length) {
        const firstKey = list[0]?.[keyField.value];
        const firstItem = list[0];
        if (firstKey !== undefined) {
          modelValue.value = firstKey;
          modelItem.value = firstItem;
        }
      }
    },
    { immediate: true },
  );

  /* ---------------- 方法 ---------------- */
  function onSelect(key: string | number, item: any) {
    if (modelValue.value !== key) {
      modelValue.value = key;
      modelItem.value = item;
      emit('select', key, item);
    }
  }

  function isActive(item: any) {
    return item?.[keyField.value] === modelValue.value;
  }

  function getIconClass(code: string) {
    if (typeof props.iconMap === 'function') return props.iconMap(code);
    return props.iconMap?.[code] || defaultIcon(code);
  }

  function defaultIcon(code: string) {
    switch (code) {
      case 'education':
        return 'i-carbon-education';
      case 'apartment':
        return 'i-carbon-building';
      case 'employment':
        return 'i-carbon-job-daemon';
      case 'member':
        return 'i-carbon-user-identification';
      case 'store':
        return 'i-carbon-store';
      default:
        return 'i-carbon-notification';
    }
  }
</script>

<style lang="less">
  .AnimatedTabs {
    &-color {
      color: @primary-color;
    }

    &-hover {
      &:hover {
        color: @primary-color;
      }
    }
  }
</style>
