<script setup lang="ts">
  /* ---------------- import ---------------- */
  import { ref, computed, provide, useSlots } from 'vue';
  import { useMediaQuery, useToggle, useEventListener } from '@vueuse/core';

  /* ---------------- props ---------------- */
  const props = withDefaults(
    defineProps<{
      /** 小于该宽度视为移动端，自动折叠 */
      mobileBreakpoint?: number;
      /** 默认侧栏宽度（px） */
      defaultWidth?: number;
      /** 侧栏最小可见宽度（px），再小就折叠 */
      minVisibleWidth?: number;
      /** 折叠时仍保留的拖拽手柄宽度（px） */
      collapsedHandleWidth?: number;

      /* —— 以下为新功能 —— */
      /** 左侧标题（优先级 < slot:left-title） */
      leftTitle?: string;
      /** 右侧标题（优先级 < slot:right-title） */
      rightTitle?: string;
      /** 是否为左右区域填充背景色 */
      fillBackground?: boolean;
    }>(),
    {
      mobileBreakpoint: 768,
      defaultWidth: 365,
      minVisibleWidth: 120,
      collapsedHandleWidth: 4,
      fillBackground: false,
    },
  );

  /* ---------------- state ---------------- */
  const isWide = useMediaQuery(`(min-width: ${props.mobileBreakpoint}px)`);
  const [leftCollapsed, toggleLeft] = useToggle(false);
  const [rightCollapsed, toggleRight] = useToggle(false);
  const sidebarWidth = ref(props.defaultWidth);

  /* 折叠前最后一次宽度，方便恢复 */
  let lastWidth = props.defaultWidth;

  /* ---------------- drag ---------------- */
  const layoutRef = ref<HTMLElement | null>(null);
  const isDragging = ref(false);
  let layoutLeft = 0; // 容器左边缘在视口中的 x
  let layoutRight = 0; // 容器右边缘在视口中的 x

  function onHandleDown(e: PointerEvent) {
    if (!isWide.value) return; // 移动端禁止拖动
    (e.target as HTMLElement).setPointerCapture(e.pointerId);

    isDragging.value = true;
    layoutLeft = layoutRef.value?.getBoundingClientRect().left ?? 0;
    layoutRight = layoutRef.value?.getBoundingClientRect().right ?? 0;
    e.preventDefault();
  }

  useEventListener(window, 'pointermove', (e: PointerEvent) => {
    if (!isDragging.value) return;

    const newWidth = Math.round(e.clientX - layoutLeft);

    if (newWidth < props.minVisibleWidth) {
      if (!leftCollapsed.value) lastWidth = sidebarWidth.value;
      // 如果右侧已经隐藏了，则左侧不能再隐藏
      if (rightCollapsed.value) return;
      toggleLeft(true);
    } else {
      toggleLeft(false);
      sidebarWidth.value = newWidth;
    }

    // 判断左侧栏是否隐藏
    if (!leftCollapsed.value) {
      // 左侧不隐藏的时候判断右侧距离到右边的距离
      // 计算鼠标到右侧的距离
      const rightDistance = layoutRight - e.clientX;
      // 判断右侧距离是否小于最小可见宽度
      if (rightDistance < props.minVisibleWidth) {
        toggleRight(true);
      } else {
        toggleRight(false);
      }
    }
  });

  useEventListener(window, 'pointerup', () => {
    isDragging.value = false;
  });

  /* ---------------- visibility ---------------- */
  const sidebarVisible = computed(() =>
    isWide.value ? !leftCollapsed.value : rightCollapsed.value,
  );

  /* ---------------- 手柄显示 ---------------- */
  const handleVisible = computed(
    () => isWide.value && (!leftCollapsed.value || !rightCollapsed.value),
  );

  /* ---------------- slots & title logic 🆕 ---------------- */
  const slots = useSlots();
  const hasLeftTitle = computed(() => !!slots['left-title'] || !!props.leftTitle);
  const hasRightTitle = computed(() => !!slots['right-title'] || !!props.rightTitle);

  /* ---------------- class helpers 🆕 ---------------- */
  // 弧度 2px
  const bgClass = computed(() =>
    props.fillBackground ? 'bg-white dark:bg-gray-800 rounded-[2px] p-[10px] ' : '',
  );

  /* ---------------- provide ---------------- */
  const ctx = {
    sidebarVisible,
    toggleSidebar: () => {
      if (!isWide.value) return;
      if (leftCollapsed.value) {
        sidebarWidth.value = lastWidth || props.defaultWidth;
      }
      toggleLeft();
    },
  };
  provide('sidebarCtx', ctx);

  const leftStyle = computed(() => {
    // 如果右侧隐藏宽度为 100%
    if (rightCollapsed.value) {
      return {
        width: `calc(100% - ${props.collapsedHandleWidth}px)`,
      };
    } else {
      return {
        width: `${sidebarWidth.value}px`,
      };
    }
  });
</script>

<template>
  <div ref="layoutRef" class="flex w-full h-full relative overflow-hidden">
    <!-- 左侧栏 -->
    <div
      v-if="sidebarVisible"
      class="shrink-0 flex flex-col h-full border-r border-gray-200 dark:border-gray-700"
      :class="bgClass"
      :style="leftStyle"
    >
      <!-- 左标题（可插槽或 props） 🆕 -->
      <div
        v-if="hasLeftTitle"
        class="px-3 py-2 border-b border-gray-200 dark:border-gray-700 font-medium"
      >
        <slot name="left-title">
          {{ props.leftTitle }}
        </slot>
      </div>

      <!-- 左内容 -->
      <div class="flex-1 flex-shrink-0 overflow-auto overflow-hidden">
        <slot name="left" v-bind="ctx"></slot>
      </div>
    </div>

    <!-- 拖拽手柄 -->
    <div
      class="drag-handle cursor-col-resize transition-colors"
      :class="{ collapsed: !handleVisible }"
      :style="{ width: handleVisible ? '4px' : `${props.collapsedHandleWidth}px` }"
      @pointerdown="onHandleDown"
    ></div>

    <!-- 右侧主区域 -->
    <div
      v-if="!rightCollapsed"
      class="flex-1 flex flex-col h-full overflow-hidden"
      :class="bgClass"
    >
      <!-- 右标题（可插槽或 props） 🆕 -->
      <div
        v-if="hasRightTitle"
        class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 font-medium"
      >
        <slot name="right-title">
          {{ props.rightTitle }}
        </slot>
      </div>

      <!-- 右内容 -->
      <div class="flex-1 flex-shrink-0 overflow-auto min-w-0 overflow-hidden">
        <slot name="right" v-bind="ctx"></slot>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .drag-handle {
    background: transparent;
  }

  .drag-handle:hover {
    background: rgb(59 130 246 / 30%); /* blue-500/30 */
  }

  .drag-handle.collapsed {
    background: transparent;
  }

  .drag-handle.collapsed:hover {
    background: rgb(59 130 246 / 30%);
  }
</style>
