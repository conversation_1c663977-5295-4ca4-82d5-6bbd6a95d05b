<script lang="ts" setup>
  import { Avatar } from 'ant-design-vue';
  import avatarImage from '@/assets/images/imageAva.png';

  interface Props {
    avatar?: string;
    username?: string;
  }
  withDefaults(defineProps<Props>(), {
    avatar: avatarImage,
    username: '--',
  });
</script>

<template>
  <div class="BasicUser">
    <Avatar shape="square" :size="56" :src="avatar" />
    <div>{{ username }}</div>
    <div class="BasicUser-main">
      <slot></slot>
    </div>
    <div class="BasicUser-extra">
      <slot name="extra"></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .BasicUser {
    display: flex;
    align-items: center;
    padding: 10px 5px;
    font-size: 18px;
    font-weight: 500;
    line-height: 22px;
    gap: 12px;

    &-image {
      width: 56px;
      height: 56px;
    }

    &-main {
      flex-grow: 1;
    }
  }
</style>
