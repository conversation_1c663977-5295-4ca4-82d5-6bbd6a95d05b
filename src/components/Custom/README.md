# BasicSelectTree 组件

一个基于 Ant Design Vue Tree 组件封装的选择树组件，具有默认选中第一项且选中后不可取消的特性。

## 功能特性

- ✅ **默认选中第一项**: 组件加载时自动选中第一个可选择的节点
- ✅ **选中后不可取消**: 点击已选中的项不会取消选择，确保始终有选中项
- ✅ **支持自定义树数据**: 可以传入自定义的树形数据结构
- ✅ **支持自定义字段名称**: 可以自定义 value、label、children 字段名
- ✅ **双向绑定**: 支持 v-model 双向绑定选中状态
- ✅ **类型安全**: 兼容 string 和 number 类型的 key 值

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `treeData` | `any[]` | `[]` | 树形数据，如果为空则使用默认演示数据 |
| `selectedKeys` | `TreeKey[]` | `[]` | 选中的节点 key 数组，支持 v-model |
| `valueField` | `string` | `'key'` | 节点 key 对应的字段名 |
| `labelField` | `string` | `'title'` | 节点标题对应的字段名 |
| `childrenField` | `string` | `'children'` | 子节点对应的字段名 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:selectedKeys` | `keys: TreeKey[]` | 选中状态变化时触发 |

## 基本使用

```vue
<template>
  <BasicSelectTree 
    v-model:selected-keys="selectedKeys"
    @update:selected-keys="handleSelectionChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import BasicSelectTree from './BasicSelectTree.vue';

const selectedKeys = ref([]);

const handleSelectionChange = (keys) => {
  console.log('选中变化:', keys);
};
</script>
```

## 使用自定义数据

```vue
<template>
  <BasicSelectTree 
    v-model:selected-keys="selectedKeys"
    :tree-data="customTreeData"
  />
</template>

<script setup>
import { ref } from 'vue';
import BasicSelectTree from './BasicSelectTree.vue';

const selectedKeys = ref([]);

const customTreeData = [
  {
    title: '部门管理',
    key: 'dept',
    children: [
      { title: '技术部', key: 'tech' },
      { title: '产品部', key: 'product' },
    ],
  },
  {
    title: '用户管理',
    key: 'user',
  },
];
</script>
```

## 自定义字段名称

```vue
<template>
  <BasicSelectTree 
    v-model:selected-keys="selectedKeys"
    :tree-data="customFieldTreeData"
    value-field="id"
    label-field="name"
    children-field="subItems"
  />
</template>

<script setup>
import { ref } from 'vue';
import BasicSelectTree from './BasicSelectTree.vue';

const selectedKeys = ref([]);

const customFieldTreeData = [
  {
    name: '系统设置',
    id: 1,
    subItems: [
      { name: '用户管理', id: 11 },
      { name: '角色管理', id: 12 },
    ],
  },
];
</script>
```

## 核心逻辑说明

### 默认选中逻辑
- 组件挂载时会自动查找第一个可选择的节点
- 使用递归算法遍历树结构，优先选择父节点，如果父节点不可选则选择第一个子节点
- 当树数据变化时会重新执行默认选中逻辑

### 不可取消逻辑
- 在 `handleSelect` 事件处理函数中，如果检测到 `selectedKeys` 为空数组，则直接返回，不更新选中状态
- 这样确保用户无法通过点击已选中项来取消选择

### 类型兼容性
- 定义了 `TreeKey` 类型为 `string | number`，兼容不同类型的节点 key 值
- 所有相关的类型定义都使用 `TreeKey` 类型，确保类型安全

## 注意事项

1. 组件会在以下时机执行默认选中逻辑：
   - 组件挂载时
   - 树数据变化时
   - 当前选中为空且有可选节点时

2. 如果外部通过 `v-model` 传入了选中状态，组件会优先使用外部状态

3. 组件继承了 Ant Design Vue Tree 组件的所有其他属性和事件

## 示例文件

查看 `BasicSelectTreeExample.vue` 文件获取完整的使用示例。
