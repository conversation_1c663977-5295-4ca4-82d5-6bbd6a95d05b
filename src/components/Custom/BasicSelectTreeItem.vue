<script lang="ts" setup>
  import { MoreOutlined } from '@ant-design/icons-vue';
  import { computed, toRaw } from 'vue';
  import { Dropdown, Menu, MenuItem } from 'ant-design-vue';
  import type { MenuProps } from 'ant-design-vue';
  import type { TreeActionItem } from './types/basicSelectTree';
  import { omit } from 'lodash-es';

  interface Props {
    record: any;
    level: number;
    selected: boolean;
    title: string;
    children?: any[];
    actions?: TreeActionItem[];
    hiddenActions?: boolean;
  }
  const props = withDefaults(defineProps<Props>(), {
    record: () => ({}),
    level: 0,
    selected: false,
    actions: () => [],
    hiddenActions: false,
  });
  const emit = defineEmits(['click']);

  // 运行一遍 actios 的 ifShow
  const actionsFilter = computed(() => {
    return props.actions
      .map((item, index) => {
        if (!item.key) {
          item.key = index + '';
        }
        return item;
      })
      .filter((item) => {
        const ifShow = item?.ifShow;
        if (ifShow === undefined) {
          return true;
        }
        if (typeof ifShow === 'function') {
          return ifShow(props.record);
        }
        return !!ifShow;
      });
  });

  // menu  点击事件
  const menuClick: MenuProps['onClick'] = (e) => {
    const key = e.key;
    const item = actionsFilter.value.find((item) => item.key === key);
    if (item?.onClick) {
      item.onClick(toRaw(props.record));
    }
  };
</script>

<template>
  <div class="flex items-center h-[30px] justify-between" @click="emit('click', record)">
    <div :class="{ 'BasicSelectTreeItem-title': selected }">{{ title }}</div>
    <Dropdown v-if="actions.length && !hiddenActions" @click.stop trigger="click">
      <MoreOutlined
        class="w-[23px] h-[23px] flex items-center justify-center rounded-[2px] BasicSelectTreeItem-button transition-all duration-300"
      />
      <template #overlay>
        <Menu @click="menuClick">
          <MenuItem
            v-bind="omit(item, ['onClick', 'ifShow', 'label'])"
            v-for="item in actionsFilter"
            :key="item.key"
          >
            {{ item.label }}
          </MenuItem>
        </Menu>
      </template>
    </Dropdown>
  </div>
</template>

<style lang="less" scoped>
  // 悬浮高亮
  .BasicSelectTreeItem {
    &-button {
      font-size: 16px;

      &:hover {
        background-color: @primary-color;
        color: #fff;
      }
    }

    &-title {
      color: @primary-color;
    }
  }
</style>
