<template>
  <div :class="getClass" :style="getStyle" ref="wrapperRef">
    <div v-if="getShowHeader" ref="headerRef" :class="getHeaderClass">
      <slot name="header"></slot>
    </div>
    <div class="overflow-hidden" :class="getContentClass" :style="getContentStyle" ref="contentRef">
      <slot></slot>
    </div>
    <div v-if="getShowFooter" class="h-70px">
      <PageFooter ref="footerRef">
        <template #left>
          <slot name="leftFooter"></slot>
        </template>
        <template #right>
          <slot name="rightFooter"></slot>
        </template>
      </PageFooter>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { PageWrapperFixedHeightKey } from '@/enums/pageEnum';
  import { useContentHeight } from '@/hooks/web/useContentHeight';
  import { useDesign } from '@/hooks/web/useDesign';
  import { propTypes } from '@/utils/propTypes';
  import { debounce } from 'lodash-es';
  import { useElementSize } from '@vueuse/core';
  import {
    CSSProperties,
    PropType,
    computed,
    provide,
    ref,
    unref,
    useAttrs,
    useSlots,
    watch,
  } from 'vue';
  import PageFooter from './PageFooter.vue';

  defineOptions({
    name: 'PageWrapper',
    inheritAttrs: false,
  });

  const props = defineProps({
    title: propTypes.string,
    dense: propTypes.bool,
    ghost: propTypes.bool,
    headerSticky: propTypes.bool.def(false),
    headerStyle: Object as PropType<CSSProperties>,
    content: propTypes.string,
    contentStyle: {
      type: Object as PropType<CSSProperties>,
    },
    contentBackground: propTypes.bool.def(true),
    contentFullHeight: propTypes.bool.def(true), // 是否撑满剩余空间
    contentClass: propTypes.string,
    fixedHeight: propTypes.bool.def(false), // 是否固定高度
    upwardSpace: propTypes.oneOfType([propTypes.number, propTypes.string]).def(0),
  });

  const attrs = useAttrs();
  const slots = useSlots();

  const wrapperRef = ref(null);
  const headerRef = ref(null);
  const contentRef = ref(null);
  const footerRef = ref(null);

  const { height } = useElementSize(wrapperRef);

  const { prefixCls } = useDesign('page-wrapper');

  provide(
    PageWrapperFixedHeightKey,
    computed(() => props.fixedHeight),
  );

  const getIsContentFullHeight = computed(() => {
    return props.contentFullHeight;
  });

  const getUpwardSpace = computed(() => props.upwardSpace);
  const { redoHeight, setCompensation, contentHeight } = useContentHeight(
    getIsContentFullHeight,
    wrapperRef,
    [headerRef, footerRef],
    [contentRef],
    getUpwardSpace,
  );
  const debounceRedoHeight = debounce(redoHeight, 50);
  setCompensation({ useLayoutFooter: true, elements: [footerRef] });

  const getClass = computed(() => {
    return [
      prefixCls,
      {
        [`${prefixCls}--dense`]: props.dense,
      },
      attrs.class ?? {},
    ];
  });

  const getStyle = computed(() => {
    const { contentFullHeight, fixedHeight } = props;
    return {
      ...(contentFullHeight && fixedHeight ? { height: '100%' } : {}),
    };
  });

  const getShowHeader = computed(() => slots?.header);

  const getShowFooter = computed(() => slots?.leftFooter || slots?.rightFooter);

  const getContentStyle = computed((): CSSProperties => {
    const { contentFullHeight, contentStyle, fixedHeight } = props;
    if (!contentFullHeight) {
      return { ...contentStyle };
    }
    console.log('contentHeight', contentHeight.value);

    const height = `${unref(contentHeight)}px`;
    return {
      ...contentStyle,
      minHeight: height,
      ...(fixedHeight ? { height } : {}),
    };
  });

  const getContentClass = computed(() => {
    const { contentBackground, contentClass } = props;
    console.log('contentClass', [
      `${prefixCls}-content`,
      contentClass,
      {
        [`${prefixCls}-content-bg`]: contentBackground,
      },
    ]);
    return [
      `${prefixCls}-content`,
      contentClass,
      {
        [`${prefixCls}-content-bg`]: contentBackground,
      },
    ];
  });

  const getHeaderClass = computed(() => {
    return `${prefixCls}-header`;
  });

  watch(
    () => [getShowFooter.value],
    () => {
      redoHeight();
    },
    {
      flush: 'post',
      immediate: true,
    },
  );

  watch(height, () => {
    const { contentFullHeight, fixedHeight } = props;
    contentFullHeight && fixedHeight && debounceRedoHeight();
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-page-wrapper';

  .@{prefix-cls} {
    position: relative;
    padding: 16px 16px 0;

    &-header {
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 2px;
      background-color: @component-background;
    }

    .@{prefix-cls}-content {
      // margin: 16px;
    }

    .ant-page-header {
      &:empty {
        padding: 0;
      }
    }

    &-content-bg {
      background-color: transparent;
    }

    &--dense {
      .@{prefix-cls}-content {
        margin: 0;
      }
    }
  }
</style>

<style lang="less" scoped>
  ::v-deep(.ant-page-header-heading) {
    // border-bottom: 1px solid @border-color-base;
    position: relative;

    &::before {
      content: '';
      display: block;
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: @border-color-base;
    }
  }
</style>
