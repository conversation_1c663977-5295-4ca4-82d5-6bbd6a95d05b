<template>
  <!-- 容器高度由 JS 计算，但内部排布全靠 CSS -->
  <div ref="wrapperRef" :class="getClass">
    <!-- header -->
    <div v-if="slots.header" :class="`${prefixCls}-header`">
      <slot name="header"></slot>
    </div>

    <!-- content：flex-1 自动填充，“overflow-auto” 控制滚动 -->
    <div class="flex-1 overflow-auto" :class="getContentClass">
      <slot></slot>
    </div>

    <!-- footer，固定高度 70px（可从 props 注入），flex-shrink: 0 -->
    <PageFooter v-if="showFooter" ref="footerRef" class="shrink-0 h-70px">
      <template #left><slot name="leftFooter"></slot></template>
      <template #right><slot name="rightFooter"></slot></template>
    </PageFooter>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, useSlots, CSSProperties } from 'vue';
  import PageFooter from './PageFooter.vue';
  import { propTypes } from '@/utils/propTypes';
  import { useDesign } from '@/hooks/web/useDesign';

  defineOptions({ name: 'PageWrapper', inheritAttrs: false });

  /* ---------------- props ---------------- */
  const props = defineProps({
    dense: propTypes.bool,
    ghost: propTypes.bool,
    headerSticky: propTypes.bool.def(false),
    headerStyle: Object as PropType<CSSProperties>,
    contentStyle: Object as PropType<CSSProperties>,
    contentBackground: propTypes.bool.def(true),
    fixedHeight: propTypes.bool.def(false),
    upwardSpace: propTypes.oneOfType([propTypes.number, propTypes.string]).def(0),
  });

  const slots = useSlots();
  const wrapperRef = ref<HTMLElement | null>(null);
  const { prefixCls } = useDesign('page-wrapper');

  /* ---------- 2. 衍生计算 ---------- */
  const getClass = computed(() => [prefixCls, { [`${prefixCls}--dense`]: props.dense }]);

  const getContentClass = computed(() => [
    `${prefixCls}-content`,
    {
      [`${prefixCls}-content-bg`]: props.contentBackground,
    },
  ]);

  const showFooter = computed(() => slots.leftFooter || slots.rightFooter);
</script>

<style lang="less">
  @prefix-cls: ~'@{namespace}-page-wrapper';

  .@{prefix-cls} {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 16px 16px 0;

    &-header {
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 2px;
      background: @component-background;
    }

    &-content-bg {
      background: transparent;
    }

    &--dense {
      .@{prefix-cls}-content {
        margin: 0;
      }
    }
  }

  /* sticky header 下方细线分割，可选 */
  ::v-deep(.ant-page-header-heading) {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 100%;
      height: 1px;
      background: @border-color-base;
    }
  }
</style>
