<template>
  <div :class="getClass">
    <div v-if="getShowHeader" ref="headerRef" :class="getHeaderClass">
      <div
        v-if="getShowTitle"
        class="flex items-center gap-[12px] font-size-[16px] justify-between"
      >
        <ArrowLeftOutlined
          class="cursor-pointer p-[5px]"
          v-if="attrs.onBack"
          :onclick="attrs.onBack"
        />
        <div v-show="title" class="flex-1">
          {{ title }}
        </div>
        <slot name="extra"></slot>
      </div>
      <slot name="header"></slot>
    </div>
    <div class="overflow-hidden" :class="getContentClass">
      <slot></slot>
    </div>
    <div v-if="getShowFooter" class="h-70px">
      <PageFooter ref="footerRef">
        <template #left>
          <slot name="leftFooter"></slot>
        </template>
        <template #right>
          <slot name="rightFooter"></slot>
        </template>
      </PageFooter>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { useDesign } from '@/hooks/web/useDesign';
  import { propTypes } from '@/utils/propTypes';
  import { CSSProperties, PropType, computed, ref, useAttrs, useSlots } from 'vue';
  import PageFooter from './PageFooter.vue';
  import { ArrowLeftOutlined } from '@ant-design/icons-vue';

  defineOptions({
    name: 'PageWrapper',
    inheritAttrs: false,
  });

  const props = defineProps({
    title: propTypes.string,
    dense: propTypes.bool,
    ghost: propTypes.bool,
    headerSticky: propTypes.bool.def(false),
    headerStyle: Object as PropType<CSSProperties>,
    content: propTypes.string,
    contentStyle: {
      type: Object as PropType<CSSProperties>,
    },
    contentBackground: propTypes.bool.def(false),
    contentFullHeight: propTypes.bool.def(false), // 是否撑满剩余空间
    contentClass: propTypes.string,
    fixedHeight: propTypes.bool.def(false), // 是否固定高度
    upwardSpace: propTypes.oneOfType([propTypes.number, propTypes.string]).def(0),
  });

  const attrs = useAttrs();
  const slots = useSlots();

  const headerRef = ref(null);
  const footerRef = ref(null);

  const { prefixCls } = useDesign('page-wrapper');

  const getClass = computed(() => {
    return [prefixCls, attrs.class ?? {}];
  });

  const getShowHeader = computed(
    () => slots?.header || props.title || attrs.onBack || slots?.extra,
  );

  const getShowTitle = computed(() => props.title || attrs.onBack || slots?.extra);

  const getShowFooter = computed(() => slots?.leftFooter || slots?.rightFooter);

  const getHeaderClass = computed(() => {
    return `${prefixCls}-header`;
  });

  const getContentClass = computed(() => {
    return [
      `${prefixCls}-content`,
      {
        [`${prefixCls}-content-bg`]: props.contentBackground,
      },
    ];
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-page-wrapper';

  .@{prefix-cls} {
    display: flex;
    position: relative;
    flex-direction: column;
    height: 100%;
    padding: 10px 10px 0;

    &-header {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 2px;
      background-color: @component-background;
      gap: 12px;
    }

    .@{prefix-cls}-content {
      flex-grow: 1;
      flex-shrink: 0;
      height: 0;
      overflow-y: auto;
    }

    .@{prefix-cls}-content-bg {
      display: flex;
      flex-direction: column;
      padding: 12px;
      border-radius: 2px;
      gap: 12px;
      background-color: @component-background;
    }
  }
</style>

<style lang="less" scoped></style>
