<template>
  <template v-if="Array.isArray(text)">
    <Tag
      v-for="(item, index) in tagItems"
      :key="index"
      :color="item.color"
      style="margin-right: 4px; margin-bottom: 4px"
    >
      {{ loading ? '' : item.text }}
      <LoadingOutlined v-if="loading" />
    </Tag>
  </template>
  <template v-else>
    <Tag :color="tagColor">{{ loading ? '' : tagText }} <LoadingOutlined v-if="loading" /></Tag>
  </template>
</template>

<script lang="ts" setup>
  import { ref, toRefs, unref, watch } from 'vue';
  import { Tag } from 'ant-design-vue';
  import { getDictByTypeCache } from '../api';
  import { cardColorEnum, tagColorEnum } from '@/enums/colorEnum';
  import { isString, isNumber, cloneDeepWith, isBoolean } from 'lodash-es';
  //导入加载icon
  import { LoadingOutlined } from '@ant-design/icons-vue';

  interface Props {
    type: string;
    text: string | number | boolean | string[];
  }

  interface TagItem {
    text: string;
    color: string;
  }

  const props = defineProps<Props>();
  const { type, text } = toRefs(props);
  const tagColor = ref<string>(tagColorEnum.LIME);
  const tagText = ref(unref(text));
  const tagItems = ref<TagItem[]>([]);
  let loading = ref(true);

  const methods = {
    async getTag() {
      try {
        let dist = methods.arrToUpperCase(await getDictByTypeCache(unref(type)));
        const currentText = unref(text);

        if (Array.isArray(currentText)) {
          // 处理 string[] 类型
          const items: TagItem[] = [];
          const cardColors = Object.values(cardColorEnum).map((color) => String(color));
          currentText.forEach((textItem, textIndex) => {
            let str = methods.textToUpperCase(textItem);
            let index = dist.findIndex((item: any) => item.value === str);
            if (index !== -1) {
              items.push({
                text: dist[index].label,
                color: cardColors[index % cardColors.length] || tagColorEnum.LIME,
              });
            } else {
              // 如果找不到对应的字典项，使用原始值
              items.push({
                text: textItem.toString(),
                color: cardColors[textIndex % cardColors.length] || tagColorEnum.LIME,
              });
            }
          });
          tagItems.value = items;
        } else {
          // 处理单个值类型
          let str = methods.textToUpperCase(currentText);
          let index = dist.findIndex((item: any) => item.value === str);
          if (index !== -1) {
            tagColor.value = cardColorEnum[index];
            tagText.value = dist[index].label;
          }
        }
      } catch (_) {
        console.error(_);
      } finally {
        loading.value = false;
      }
    },
    textToUpperCase(text: string | number | boolean) {
      if (isString(text)) {
        return text.toUpperCase();
      } else if (isNumber(text)) {
        return text.toString();
      } else if (isBoolean(text)) {
        // 布尔值转换为字符串
        return text.toString().toUpperCase();
      }
      return text;
    },
    arrToUpperCase(arr: any[]) {
      return cloneDeepWith(arr, (value) => {
        if (isString(value)) {
          return value.toUpperCase();
        } else if (isNumber(value)) {
          return value.toString();
        }
      });
    },
  };
  watch(
    () => text.value,
    () => {
      methods.getTag();
    },
    {
      immediate: true,
    },
  );
</script>
