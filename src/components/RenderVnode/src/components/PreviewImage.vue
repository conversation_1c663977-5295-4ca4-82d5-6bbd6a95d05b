<template>
  <ImagePreviewGroup>
    <Image
      :style="style"
      :fallback="fallback"
      :placeholder="placeholder"
      v-for="(item, index) in urlList"
      :src="item"
      :key="index"
    />
  </ImagePreviewGroup>
</template>

<script lang="ts" setup>
  import { Image, ImagePreviewGroup } from 'ant-design-vue';
  import type { CSSProperties } from 'vue';

  interface Props {
    urlList: string[];
    style: CSSProperties;
    fallback: string;
    placeholder: boolean;
  }

  withDefaults(defineProps<Props>(), {
    urlList: () => [],
    style: () => ({}),
    fallback: '',
    placeholder: false,
  });
</script>
