<template>
  <div>
    <span @click="method.onClick()" class="PreviewImageWithText-wrapper">
      {{ showText }}
    </span>
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    showText: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['runCallBack']);
  const method = {
    onClick() {
      emits('runCallBack');
    },
  };
</script>

<style lang="less" scoped>
  .PreviewImageWithText-wrapper {
    display: inline-block;
    text-decoration: underline;
    cursor: pointer;

    &:hover {
      color: @primary-color;
    }
  }
</style>
