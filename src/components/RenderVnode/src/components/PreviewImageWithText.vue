<template>
  <div>
    <span @click="method.onClick(true)" class="PreviewImageWithText-wrapper">
      {{ showText }}
    </span>
    <ImagePreviewGroup
      :preview="{
        visible,
        onVisibleChange: method.onClick,
      }"
      :style="{ display: 'none' }"
    >
      <Image
        :style="{ display: 'none' }"
        v-for="(item, index) in urlList"
        :src="item"
        :key="index"
      />
    </ImagePreviewGroup>
  </div>
</template>

<script lang="ts" setup>
  import { Image, ImagePreviewGroup } from 'ant-design-vue';
  import { ref } from 'vue';

  interface Props {
    urlList: string[];
    showText: string;
  }

  withDefaults(defineProps<Props>(), {
    urlList: () => [],
    showText: '',
  });
  const visible = ref<boolean>(false);
  const method = {
    onClick(val) {
      visible.value = val;
    },
  };
</script>

<style lang="less" scoped>
  .PreviewImageWithText-wrapper {
    display: inline-block;
    text-decoration: underline;
    cursor: pointer;

    &:hover {
      color: @primary-color;
    }
  }
</style>
