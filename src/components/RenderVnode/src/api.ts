/**
 * 请求封装缓存
 */
import { apiGetDicts } from '@/api/admin/dict';

const cache = new Map();
// 判断是否正在请求中
const requestMap = new Map();

export const getDictByTypeCache = (type: string) => {
  if (cache.has(type)) {
    return new Promise((resolve) => {
      resolve(cache.get(type));
    });
  } else {
    if (requestMap.has(type)) {
      return requestMap.get(type);
    } else {
      const request = apiGetDicts(type);
      requestMap.set(type, request);
      request
        .then((res) => {
          cache.set(type, res);
        })
        .finally(() => {
          requestMap.delete(type);
        });
      return request;
    }
  }
};

// 任意api 缓存 请求封装
const cacheAny = new Map();
const requestMapAny = new Map();

export const getAnyByTypeCache = ({
  key,
  api,
  params,
}: {
  key: string;
  api: (any) => Promise<any>;
  params?: any;
}) => {
  if (cacheAny.has(key)) {
    return new Promise((resolve) => {
      resolve(cacheAny.get(key));
    });
  } else {
    if (requestMapAny.has(key)) {
      return requestMapAny.get(key).request;
    } else {
      const request = api(params);
      requestMapAny.set(key, request);
      request
        .then((res) => {
          cacheAny.set(key, res);
        })
        .finally(() => {
          requestMapAny.delete(key);
        });
      return request;
    }
  }
};
