import { FormProps, FormActionType } from '@/components/Form/src/types/form';
import type { DescriptionsProps } from 'ant-design-vue/es/descriptions/index';
import type { CollapseContainerOptions } from '@/components/Container/index';
import type { VNode, CSSProperties } from 'vue';

export interface SModalInstance {
  openModal: (data?: any) => void;
  closeModal: () => void;
  errorModal: (arg0?: string) => void;
  openInitFn?: (arg0?: any) => void;
}

export interface SModalMethods {
  openModal: (data?: any) => void;
  closeModal: () => void;
}
export type RegisterModalFn = (sModalInstance: SModalInstance, uuid: string) => void;

export type UseModalReturnType = [RegisterModalFn, SModalMethods];
export interface methodsInner {
  closeModal: () => void;
  errorModal: (arg0?: string) => void;
}
export type UseModalInnerReturnType = [RegisterModalFn, methodsInner];

// SmodalFrom

export interface SModalFormInstance {
  openModal: (OpenDrawerData) => void;
  init: (props) => void;
}
export interface mergeObj {
  [key: string]: string | boolean | number | Array<string> | null | undefined;
}
type merge = mergeObj | Function;

type callbackFn = (callback: { isUpdate: boolean; record?: any; status: boolean }) => void;
export interface SModalFormProps extends FormProps {
  addFn?: Function;
  updateFn?: Function;
  saveButtonText?: string;
  backButtonText?: string;
  buttonStyle?: string;
  addText?: string;
  updateText?: string;
  callbackFn?: callbackFn;
  destroyOnClose?: boolean;
  showCloseButton?: boolean;
  merge?: merge;
}
export interface OpenModalFormData {
  isUpdate: boolean;
  record?: any;
  merge?: merge;
  title?: string;
  beforeFn?: beforeFn;
}
export interface OpenModalMergeData {
  record?: any;
  merge?: merge;
  title?: string;
}

//定义一个type，描述方法beforeFn
type beforeFn = (action: FormActionType, data: OpenModalFormData) => void;

export interface FormMethods {
  addModal: (record?: OpenModalMergeData) => void;
  updateModal: (record: OpenModalMergeData) => void;
}
export type RegisterFormFn = (sModalInstance: SModalFormInstance, uuid: number) => void;

export type UseModalFormReturnType = [RegisterFormFn, FormMethods];

// SmodalDes

export interface SModalDesInstance {
  openModal: (OpenDrawerData) => void;
  init: (props) => void;
}
export interface SModalDesProps extends DescriptionsProps {
  buttonStyle?: CSSProperties;
  useCollapse?: boolean;
  /**
   * item configuration
   * @type DescItem
   */
  schema: DescItem[];
  /**
   * 数据
   * @type object
   */
  data?: Recordable;
  /**
   * Built-in CollapseContainer component configuration
   * @type CollapseContainerOptions
   */
  collapseOptions?: CollapseContainerOptions;
}

export interface DescItem {
  labelMinWidth?: number;
  contentMinWidth?: number;
  labelStyle?: CSSProperties;
  field: string;
  label: string | VNode | JSX.Element;
  // Merge column
  span?: number;
  show?: (...arg: any) => boolean;
  // render
  render?: (
    val: any,
    data: Recordable,
  ) => VNode | undefined | JSX.Element | Element | string | number;
}

export interface OpenModalDesData {
  record?: any;
  title?: string;
}

export interface DesMethods {
  openModal: (record?: OpenModalDesData) => void;
}
export type RegisterDesFn = (sModalInstance: SModalDesInstance, uuid: string) => void;

export type UseModalDesReturnType = [RegisterDesFn, DesMethods];
