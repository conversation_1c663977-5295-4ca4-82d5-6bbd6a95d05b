<template>
  <Modal @cancel="cancel" :open="visible" v-bind="$attrs" centered>
    <div class="p-8">
      <slot name="default"></slot>
    </div>
    <template #footer>
      <slot
        name="insertButton"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        :size="propsConfig.size"
      ></slot>
      <a-button
        preIcon="ant-design:close-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="16"
        :size="propsConfig.size"
        @click="cancel"
        :disabled="loading"
        v-if="isCancel"
      >
        {{ showCancelText }}
      </a-button>
      <slot
        name="button"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        :size="propsConfig.size"
      ></slot>
      <a-button
        preIcon="ant-design:check-outlined"
        type="primary"
        :style="propsConfig.buttonStyle"
        :iconSize="16"
        :size="propsConfig.size"
        :loading="loading"
        @click="ok"
        v-if="isOk"
      >
        {{ showOkText }}
      </a-button>
      <slot
        name="beforeButton"
        :style="propsConfig.buttonStyle"
        :iconSize="16"
        :size="propsConfig.size"
      ></slot>
    </template>
  </Modal>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive, getCurrentInstance, computed } from 'vue';
  import { Modal } from 'ant-design-vue';
  import { SModalInstance } from '../types/typing';
  import { useI18n } from '@/hooks/web/useI18n';

  const { t } = useI18n();

  export default defineComponent({
    name: 'SModal',
    components: { Modal },
    props: {
      isLoading: {
        type: Boolean,
        default: false,
      },
      isCancel: {
        type: Boolean,
        default: true,
      },
      isOk: {
        type: Boolean,
        default: true,
      },
      cancelText: {
        type: String,
      },
      okText: {
        type: String,
      },
      // 是否完全控制
      isControl: {
        type: Boolean,
        default: false,
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['register', 'onOk', 'onCancel'],
    setup(props, { emit }) {
      const instance = getCurrentInstance();
      let visible = ref<boolean>(false);
      const showOkText = computed(() => {
        if (props.okText) {
          return props.okText;
        } else {
          return t('common.okText');
        }
      });

      const showCancelText = computed(() => {
        if (props.cancelText) {
          return props.cancelText;
        } else {
          return t('common.cancelText');
        }
      });
      const propsConfig = reactive({
        size: 'default',
        // 组件默认props
        buttonStyle: 'min-width: 100px',
      });
      const sModalInstance: SModalInstance = {
        openModal,
        closeModal,
        errorModal,
        openInitFn: undefined,
      };
      instance && emit('register', sModalInstance, instance.uid);

      function openModal(data?: any) {
        visible.value = true;
        sModalInstance.openInitFn && sModalInstance.openInitFn(data);
      }
      function closeModal() {
        visible.value = false;
      }
      function errorModal(content = '异常错误，请联系开发者') {
        Modal.error({
          title: '错误',
          content: content,
          onOk() {
            closeModal();
          },
          centered: true,
        });
      }
      function ok() {
        emit('onOk');
        !props.isControl && closeModal();
      }
      function cancel() {
        if (props.loading) {
          return;
        }
        !props.isControl && closeModal();
        emit('onCancel');
      }
      return {
        visible,
        closeModal,
        propsConfig,
        ok,
        cancel,
        showOkText,
        showCancelText,
      };
    },
  });
</script>

<style lang="less" scoped></style>
