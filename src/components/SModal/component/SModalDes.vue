<template>
  <Modal :title="getTitle" width="600px" v-model:open="visible" v-bind="$attrs" centered>
    <div class="p-8">
      <Description @register="registerDesc" />
    </div>

    <template #footer>
      <a-button
        preIcon="ant-design:close-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="16"
        @click="closeModal()"
        v-if="isClose"
      >
        {{ closeText }}
      </a-button>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, getCurrentInstance, toRefs, nextTick, toRaw } from 'vue';
  import { Modal } from 'ant-design-vue';
  import { SModalDesInstance, OpenModalDesData, SModalDesProps } from '../types/typing';
  import { Description, useDescription } from '@/components/Description/index';

  /* ----------------------------- 组件名 ----------------------------- */
  defineOptions({ name: 'SModalView' });

  /* ------------------------------ props ------------------------------ */
  const props = withDefaults(
    defineProps<{
      isClose?: boolean;
      closeText?: string;
    }>(),
    { isClose: true, closeText: '关闭' },
  );
  const { isClose, closeText } = toRefs(props);

  /* ------------------------------ emits ------------------------------ */
  const emit = defineEmits<{
    (e: 'register', instance: SModalDesInstance, uid: number): void;
    (e: 'onClose', cfg: SModalDesProps): void;
  }>();

  /* ----------------------------- 状态定义 ----------------------------- */
  const instance = getCurrentInstance();
  const visible = ref(false);

  const propsConfig = reactive<SModalDesProps>({
    size: 'small',
    column: 1,
    labelStyle: { width: '180px' },
    buttonStyle: {}, // 供模板绑定,
    schema: [],
  });

  const descData = ref<Record<string, any>>({});
  const getTitle = ref('预览');

  let registerDesc: any;
  let descMethod: any;

  /* --------------------------- 组件对外 API --------------------------- */
  function init(descriptionProps: SModalDesProps) {
    Object.assign(propsConfig, descriptionProps);
    const rawCfg = toRaw(propsConfig) as unknown as SModalDesProps;
    // 合并配置并初始化 useDescription
    [registerDesc, descMethod] = useDescription({
      ...rawCfg,
      data: descData,
    });
  }

  function openModal(data: OpenModalDesData) {
    visible.value = true;
    const { record, title } = data;
    if (title) getTitle.value = title;
    descData.value = record || {};
    // 等待下一帧，以确保 Description 组件已渲染
    nextTick(() => descMethod?.setDescProps?.({ data: descData.value }));
  }

  function closeModal() {
    visible.value = false;
    const rawCfg = toRaw(propsConfig) as unknown as SModalDesProps;
    emit('onClose', rawCfg);
  }

  /* ------------------------ 向父组件注册自身 ------------------------- */
  const sModalInstance: SModalDesInstance = { openModal, init };
  if (instance) emit('register', sModalInstance, instance.uid);

  /* ------------------------------ expose ------------------------------ */
  defineExpose({ openModal, init });
</script>

<style lang="less" scoped>
  :deep(.ant-descriptions-view > table) {
    table-layout: auto;
  }
</style>
