<template>
  <Modal
    :title="getTitle"
    v-model:open="visible"
    v-bind="$attrs"
    :destroyOnClose="propsConfig.destroyOnClose"
    centered
    width="600px"
    :closable="propsConfig.showCloseButton"
  >
    <div class="px-8 p-t-8 p-b-4">
      <slot name="before"></slot>
      <BasicForm @register="registerForm" @submit="onSubmit" />
      <slot name="after"></slot>
    </div>

    <template #footer>
      <a-button
        v-if="propsConfig.showCloseButton"
        preIcon="ant-design:arrow-left-outlined"
        :iconSize="16"
        @click="closeModal()"
      >
        {{ propsConfig.backButtonText }}
      </a-button>
      <a-button
        preIcon="ant-design:check-outlined"
        :iconSize="16"
        :loading="loading"
        type="primary"
        @click="handlerSubmit()"
      >
        {{ propsConfig.saveButtonText }}
      </a-button>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, getCurrentInstance, nextTick, unref, toRaw } from 'vue';
  import { Modal, message } from 'ant-design-vue';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { isEmpty, isPlainObject, isFunction, cloneDeep, isUndefined, omitBy } from 'lodash-es';
  import type { SModalFormInstance, SModalFormProps, OpenModalFormData } from '../types/typing';
  import { useI18n } from '@/hooks/web/useI18n';

  defineOptions({ name: 'SModalForm' });

  /* -------------------------------- emits -------------------------------- */
  const emit = defineEmits<{
    (e: 'register', instance: SModalFormInstance, uid: number): void;
    (
      e: 'success',
      payload: {
        isUpdate: boolean;
        status: boolean;
        result: Record<string, any>;
      },
    ): void;
  }>();

  /* -------------------------------- state -------------------------------- */
  const { t } = useI18n();
  const instance = getCurrentInstance();

  const visible = ref(false);
  const loading = ref(false);
  const isUpdate = ref(false);

  const propsConfig = reactive<SModalFormProps>({
    autoFocusFirstItem: false,
    rowProps: { gutter: 20 },
    layout: 'vertical',
    baseColProps: { span: 24 },
    showActionButtonGroup: false,
    labelAlign: 'left',
    addText: t('component.s_modal_form.title_add'),
    updateText: t('component.s_modal_form.title_edit'),
    saveButtonText: t('component.s_modal_form.btn_save'),
    backButtonText: t('component.s_modal_form.btn_cancel'),
    destroyOnClose: false,
    showCloseButton: true,
    // 回车提交
    autoSubmitOnEnter: true,
  });

  const getTitle = computed(() => (unref(isUpdate) ? propsConfig.updateText : propsConfig.addText));

  /* -------------------------------- form -------------------------------- */
  let registerForm: any;
  let formMethod: any;
  let addFn: any;
  let updateFn: any;
  let callbackFn: any;
  let merge: any;
  let defaultMerge: any = undefined; // 用于表单合并 （可选) useSModalForm 时传入

  /* ------------------------------ public API ------------------------------ */
  function init(config: SModalFormProps) {
    addFn = config.addFn;
    updateFn = config.updateFn;
    callbackFn = config.callbackFn;

    Object.assign(propsConfig, config);
    const rawCfg = toRaw(propsConfig) as SModalFormProps;
    [registerForm, formMethod] = useForm(rawCfg);
  }

  function openModal(data: OpenModalFormData) {
    visible.value = true;

    const { isUpdate: _isUpdate, record, beforeFn, title } = data;
    merge = data.merge;
    isUpdate.value = _isUpdate;

    if (title && _isUpdate) propsConfig.updateText = title;
    if (title && !_isUpdate) propsConfig.addText = title;

    nextTick(async () => {
      formMethod.resetFields();
      if (beforeFn) await beforeFn(formMethod, data);
      if (!isEmpty(record)) formMethod.setFieldsValue(record);
    });
  }

  function closeModal() {
    visible.value = false;
  }

  function handlerSubmit() {
    if (formMethod?.submit) {
      formMethod.submit();
    } else {
      message.error('fromMethod.submit 不存在于 SDrawerForm，请联系开发者解决异常');
    }
  }

  async function onSubmit(values: Record<string, any>) {
    let _result: Record<string, any> = {};
    try {
      loading.value = true;
      let result = cloneDeep(values);
      /* 默认 merge 处理 */
      if (defaultMerge && isPlainObject(defaultMerge)) {
        Object.assign(result, defaultMerge);
      } else if (isFunction(defaultMerge)) {
        result = (await defaultMerge(result)) || {};
      }
      if (isPlainObject(merge) && !isEmpty(merge)) {
        Object.assign(result, merge);
      } else if (isFunction(merge)) {
        result = (await merge(result)) || {};
      }
      result = omitBy(result, isUndefined);
      _result = result;

      if (unref(isUpdate)) {
        await updateFn(result);
      } else {
        await addFn(result);
      }

      callbackFn?.({ isUpdate: unref(isUpdate), status: true, result });
      closeModal();
      emit('success', { isUpdate: unref(isUpdate), status: true, result });
    } catch (error) {
      console.error(error);
      callbackFn?.({ isUpdate: unref(isUpdate), status: false, result: _result });
    } finally {
      loading.value = false;
    }
  }

  /* ---------------------------- register self ---------------------------- */
  const sModalInstance: SModalFormInstance = { openModal, init };
  if (instance) emit('register', sModalInstance, instance.uid);

  /* ------------------------------ expose --------------------------------- */
  defineExpose({ openModal, init });
</script>

<style lang="less" scoped></style>
