import { ref, unref, reactive, toRaw, getCurrentInstance, watchEffect, nextTick } from 'vue';
import { isProdMode } from '@/utils/env';
import { isFunction } from '@/utils/is';
import { tryOnUnmounted } from '@vueuse/core';
import { SModalInstance, UseModalReturnType, UseModalInnerReturnType } from '../types/typing';

const dataTransferRef = reactive<any>({});
export function useSModal(): UseModalReturnType {
  const refSModal = ref<SModalInstance | null>(null);
  const uid = ref<string>('');
  function register(drawerInstance: SModalInstance, uuid: string) {
    isProdMode() &&
      tryOnUnmounted(() => {
        dataTransferRef[unref(uid)] = null;
        refSModal.value = null;
      });
    uid.value = uuid;
    refSModal.value = drawerInstance;
  }
  function getInstance() {
    const instance = unref(refSModal);
    if (!instance) {
      console.error('useStable instance is undefined!');
    }
    return instance;
  }
  const methods = {
    openModal: <T = any>(data?: T) => {
      getInstance()?.openModal(data);
      if (data) {
        dataTransferRef[unref(uid)] = toRaw(data);
      } else if (dataTransferRef[unref(uid)]) {
        dataTransferRef[unref(uid)] = null;
      }
    },
    closeModal: () => {
      getInstance()?.closeModal();
    },
  };
  return [register, methods];
}
export function useSModalInner({
  open: openFn,
  // 数据变化
  data: callbackFn,
}: {
  open?: (data?: any) => void;
  data?: (data?: any) => void;
}): UseModalInnerReturnType {
  const sDrawerInstanceRef = ref<SModalInstance | null>(null);
  const currentInstance = getCurrentInstance();
  const uidRef = ref<string>('');
  if (!currentInstance) {
    throw new Error('useDrawerInner() can only be used inside setup() or functional components!');
  }
  const getInstance = () => {
    const instance = unref(sDrawerInstanceRef);
    if (!instance) {
      console.error('useDrawerInner instance is undefined!');
      return;
    }
    return instance;
  };
  const register = (modalInstance: SModalInstance, uuid: string) => {
    isProdMode() &&
      tryOnUnmounted(() => {
        sDrawerInstanceRef.value = null;
      });
    uidRef.value = uuid;
    sDrawerInstanceRef.value = modalInstance;
    modalInstance.openInitFn = (value: any) => {
      if (openFn && isFunction(openFn)) {
        openFn(value);
      }
    };
    currentInstance.emit('register', modalInstance, uuid);
  };
  watchEffect(() => {
    const data = dataTransferRef[unref(uidRef)];
    if (!data) return;
    if (!callbackFn || !isFunction(callbackFn)) return;
    nextTick(() => {
      callbackFn(data);
    });
  });
  return [
    register,
    {
      closeModal: () => {
        getInstance()?.closeModal();
      },
      errorModal: (text?: string) => {
        getInstance()?.errorModal(text);
      },
    },
  ];
}
