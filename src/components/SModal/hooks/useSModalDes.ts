import { ref, unref } from 'vue';
import { isProdMode } from '@/utils/env';
import { tryOnUnmounted } from '@vueuse/core';
import {
  SModalDesInstance,
  UseModalDesReturnType,
  SModalDesProps,
  OpenModalDesData,
} from '../types/typing';

export function useSModalDes(props: SModalDesProps): UseModalDesReturnType {
  const refSModalDes = ref<SModalDesInstance | null>(null);
  const uid = ref<string>('');
  function register(drawerInstance: SModalDesInstance, uuid: string) {
    isProdMode() &&
      tryOnUnmounted(() => {
        refSModalDes.value = null;
      });
    uid.value = uuid;
    refSModalDes.value = drawerInstance;
    drawerInstance.init(props);
  }
  function getInstance() {
    const instance = unref(refSModalDes);
    if (!instance) {
      console.error('useSModalDes instance is undefined!');
    }
    return instance;
  }
  const methods = {
    openModal: (v?: OpenModalDesData) => {
      getInstance()?.openModal(v);
    },
  };
  return [register, methods];
}
