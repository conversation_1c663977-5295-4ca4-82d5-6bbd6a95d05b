import { ref, unref } from 'vue';
import { isProdMode } from '@/utils/env';
import { isEmpty } from 'lodash-es';
import { tryOnUnmounted } from '@vueuse/core';
import {
  SModalFormInstance,
  UseModalFormReturnType,
  SModalFormProps,
  OpenModalMergeData,
} from '../types/typing';

export function useSModalForm(props: SModalFormProps): UseModalFormReturnType {
  const refSModal = ref<SModalFormInstance | null>(null);
  const uid = ref<number>();
  function register(drawerInstance: SModalFormInstance, uuid: number) {
    isProdMode() &&
      tryOnUnmounted(() => {
        refSModal.value = null;
      });
    uid.value = uuid;
    refSModal.value = drawerInstance;
    drawerInstance.init(props);
  }
  function getInstance() {
    const instance = unref(refSModal);
    if (!instance) {
      console.error('useDrawerInner instance is undefined!');
    }
    return instance;
  }
  const methods = {
    addModal: (v?: OpenModalMergeData) => {
      if (isEmpty(v)) {
        getInstance()?.openModal({
          isUpdate: false,
        });
      } else {
        const { record, merge, title } = v as OpenModalMergeData;
        getInstance()?.openModal({
          isUpdate: false,
          record,
          merge,
          title,
        });
      }
    },
    updateModal: ({ record, merge, title }: OpenModalMergeData) => {
      getInstance()?.openModal({
        isUpdate: true,
        record,
        merge,
        title,
      });
    },
  };
  return [register, methods];
}
