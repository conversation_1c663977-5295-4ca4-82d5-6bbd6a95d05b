<template>
  <div
    class="SlinVerify"
    @mousemove="handlerMouse('move', $event)"
    @mouseup="handlerMouse('up', $event)"
    @mouseleave="handlerMouse('leave', $event)"
    @touchmove="handlerTouch('move', $event)"
    @touchend="handlerTouch('end', $event)"
    @touchcancel="handlerTouch('cancel', $event)"
  >
    <div class="SlinVerify-body">
      <div class="SlinVerify-title">
        <div>请完成安全验证</div>
        <div>
          <close-outlined @click="handleClose" />
        </div>
      </div>

      <div class="SlinVerify-imgs" v-show="loading">
        <img ref="slidingImg" :src="`data:image/png;base64,${verifyImg.jigsawImageBase64}`" />
        <img :src="`data:image/png;base64,${verifyImg.originalImageBase64}`" />
      </div>
      <div class="SlinVerify-imgs" v-show="!loading">
        <div class="SlinVerify-loadingBox"><loading-outlined /> </div>
      </div>
      <div class="SlinVerify-sliding">
        <div ref="slidingBox"></div>
        <div
          @touchstart="handlerTouch('start', $event)"
          ref="slidingBlock"
          @mousedown="handlerMouse('down', $event)"
        >
          <check-outlined v-if="messageType === 'success'" />
          <close-outlined v-else-if="messageType === 'error'" />
          <right-outlined v-else />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {
    CloseOutlined,
    LoadingOutlined,
    RightOutlined,
    CheckOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { getVerify, reqCheck } from '@/api/sys/user';
  import { aesEncryptPigx } from '@/utils/cipher';
  import { onMounted, reactive, ref } from 'vue';
  import { mouseOptions, VerifyImgType } from './model';

  const props = defineProps({
    success: {
      type: Function,
      default: () => {},
    },
    cancel: {
      type: Function,
      default: () => {},
    },
    fail: {
      type: Function,
      default: () => {},
    },

    el: {
      type: HTMLElement,
    },
    close: {
      type: Function,
    },
  });
  // 图片加载状态
  let loading = ref(false);
  let reqCheckLoading = ref(false);
  // 提示信息类型
  let messageType = ref('default');
  // div
  const slidingBox = ref();
  const slidingBlock = ref();
  const slidingImg = ref();

  // dom
  let slidingBoxDom: HTMLElement;
  let slidingBlockDom: HTMLElement;
  let slidingImgDom: HTMLElement;
  // 鼠标位置信息
  const mouse = reactive<mouseOptions>({
    x: 0,
    y: 0,
    down: false,
    endX: 0,
  });

  const verifyImg = reactive<VerifyImgType>({
    jigsawImageBase64: '',
    originalImageBase64: '',
    token: '',
    secretKey: '',
  });

  onMounted(() => {
    getImg();
    slidingBoxDom = slidingBox.value as HTMLElement;
    slidingBlockDom = slidingBlock.value as HTMLElement;
    slidingImgDom = slidingImg.value as HTMLElement;
  });

  function handleClose() {
    props?.cancel();
    destroy();
  }
  // 销毁 dom
  function destroy() {
    // 使用 props.close 销毁
    if (props?.close) {
      props.close();
      return;
    }
  }
  function getImg() {
    getVerify()
      .then((res) => {
        verifyImg.jigsawImageBase64 = res.repData.jigsawImageBase64;
        verifyImg.originalImageBase64 = res.repData.originalImageBase64;
        verifyImg.token = res.repData.token;
        verifyImg.secretKey = res.repData.secretKey;
        loading.value = true;
      })
      .catch(() => {
        message.error('获取图片失败');
      })
      .finally(() => {});
  }
  // 验证逻辑
  function verify(x) {
    reqCheckLoading.value = true;
    mouse.endX = x;
    reqCheck({
      pointJson: aesEncryptPigx(JSON.stringify({ x: x, y: 5.0 }), verifyImg.secretKey),
      captchaType: 'blockPuzzle',
      token: verifyImg.token,
    })
      .then((res) => {
        const { repCode, repMsg } = res;
        if (repCode === '0000') {
          onSuccess();
        } else {
          onFail(repMsg);
        }
      })
      .catch(() => {
        onFail('其他错误');
      });
  }
  // 验证失败处理
  function onFail(msg = '验证失败') {
    props?.fail();
    showMsg('error', msg);
    setTimeout(() => {
      refresh();
    }, 1000);
  }
  function showMsg(type = 'error', msg) {
    messageType.value = type;
    message[type](msg);
    switch (type) {
      case 'error':
        slidingBoxDom.style.borderLeft = '1px solid red';
        slidingBoxDom.style.borderTop = '1px solid red';
        slidingBoxDom.style.borderBottom = '1px solid red';
        slidingBlockDom.style.backgroundColor = 'red';
        break;
      case 'success':
        slidingBoxDom.style.borderLeft = '1px solid #2d8f06';
        slidingBoxDom.style.borderTop = '1px solid #2d8f06';
        slidingBoxDom.style.borderBottom = '1px solid #2d8f06';
        slidingBlockDom.style.backgroundColor = '#2d8f06';
        break;
      default:
        break;
    }
  }
  // 验证成功处理
  function onSuccess() {
    showMsg('success', '验证成功');
    setTimeout(() => {
      const code = aesEncryptPigx(
        verifyImg.token + '---' + JSON.stringify({ x: mouse.endX, y: 5.0 }),
        verifyImg.secretKey,
      );
      props?.success(code);
      destroy();
    }, 1000);
  }
  //滑动操作逻辑
  function handlerMouse(mouseType, e) {
    if (mouseType === 'down' && !reqCheckLoading.value && loading.value) {
      console.log('down', e.clientX);
      // 鼠标按下位置
      setBlock(mouseType, e.clientX);
    } else if (mouseType === 'move' && mouse.down) {
      // 鼠标移动位置
      setBlock(mouseType, e.clientX);
    } else if (mouseType === 'up' && mouse.down) {
      // 鼠标抬起位置
      const upX = e.clientX - mouse.x;
      verify(upX);
      setBlock(mouseType, 0);
    } else if (mouseType === 'leave' && mouse.down) {
      // console.log('leave');
      // 鼠标离开位置
      const leaveX = e.clientX - mouse.x;
      verify(leaveX);
      setBlock(mouseType, 0);
    }
  }

  // 触摸操作逻辑
  function handlerTouch(touchType, e) {
    if (touchType === 'start' && !reqCheckLoading.value && loading.value) {
      // 触摸按下位置
      setBlock(touchType, e.touches[0].clientX);
    } else if (touchType === 'move' && mouse.down) {
      // 触摸移动位置
      setBlock(touchType, e.touches[0].clientX);
    } else if (touchType === 'end' && mouse.down) {
      // 触摸抬起位置
      const upX = e.changedTouches[0].clientX - mouse.x;
      verify(upX);
      setBlock(touchType, 0);
    } else if (touchType === 'cancel' && mouse.down) {
      // console.log('leave');
      // 触摸取消
      const leaveX = e.changedTouches[0].clientX - mouse.x;
      verify(leaveX);
      setBlock(touchType, 0);
    }
  }

  // 刷新
  function refresh() {
    messageType.value = 'default';
    reqCheckLoading.value = false;
    getImg();
    resetBlock();
  }
  // 设置滑块位置
  function setBlock(mouseType, x) {
    if (mouseType === 'down' || mouseType === 'start') {
      mouse.x = x;
      mouse.down = true;
      slidingBoxDom.style.transition = 'none';
      slidingBoxDom.style.width = '0px';
      slidingBoxDom.style.borderLeft = '1px solid #1890ff';
      slidingBoxDom.style.borderTop = '1px solid #1890ff';
      slidingBoxDom.style.borderBottom = '1px solid #1890ff';

      slidingBlockDom.style.transition = 'none';
      slidingBlockDom.style.backgroundColor = '#1890ff';
      slidingBlockDom.style.color = '#fff';

      slidingImgDom.style.transition = 'none';
    } else if (mouseType === 'move' && mouse.down) {
      // 鼠标移动位置
      const moveX = x - mouse.x;
      if (0 <= moveX && moveX <= 270) {
        slidingBoxDom.style.width = moveX + 'px';
        slidingBlockDom.style.left = moveX + 'px';
        slidingImgDom.style.left = moveX + 'px';
      } else if (0 >= moveX) {
        slidingBoxDom.style.width = '0px';
        slidingBlockDom.style.left = '0px';
        slidingImgDom.style.left = '0px';
      } else if (moveX >= 270) {
        slidingBoxDom.style.width = '270px';
        slidingBlockDom.style.left = '270px';
        slidingImgDom.style.left = '270px';
      }
    } else if ((mouseType === 'up' || mouseType === 'end') && mouse.down) {
      // 鼠标抬起位置
      mouse.down = false;
    } else if ((mouseType === 'leave' || mouseType === 'cancel') && mouse.down) {
      // console.log('leave');
      // 鼠标离开位置
      mouse.down = false;
    }
  }
  // 重置滑块样式
  function resetBlock() {
    slidingBoxDom.style.transition = 'all 0.5s';
    slidingBlockDom.style.transition = 'all 0.5s';
    slidingImgDom.style.transition = 'all 0.5s';

    slidingBoxDom.style.borderLeft = '';
    slidingBoxDom.style.borderTop = '';
    slidingBoxDom.style.borderBottom = '';
    slidingBoxDom.style.width = '0px';

    slidingBlockDom.style.left = '0px';
    slidingBlockDom.style.backgroundColor = '#fff';
    slidingBlockDom.style.color = '#666';
    slidingImgDom.style.left = '0px';
    mouse.down = false;
    mouse.endX = 0;
  }
</script>

<style lang="less" scoped>
  .SlinVerify {
    display: flex;
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: rgb(0 0 0 / 50%);

    &-body {
      width: 400px;
      background-color: #fff;
    }

    &-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      padding: 0 5px 0 20px;
      border-bottom: 1px solid #e8e8e8;
      color: #45494c;
      font-size: 16px;

      & > div {
        &:first-child {
          font-weight: 600;
        }

        &:last-child {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 5px;
          font-size: 20px;
          cursor: pointer;

          &:hover {
            background-color: #efefef;
            color: #1890ff;
          }
        }
      }
    }

    &-imgs {
      position: relative;
      width: 310px;
      height: 155px;
      margin: 10px auto;
      background-color: red;

      > img {
        position: absolute;
        top: 0;
        left: 0;
        height: 155px;
        // 禁止鼠标选中
        user-select: none;
        // 禁止拖动
        -webkit-user-drag: none;
      }

      > img:first-child {
        z-index: 10;
      }

      > img:last-child {
        z-index: 0;
        width: 310px;
      }
    }

    &-loadingBox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: #94989a;
      color: #fff;
      font-size: 30px;
    }

    &-sliding {
      display: flex;
      position: relative;
      width: 310px;
      height: 40px;
      margin: 0 auto 10px;
      overflow: hidden;
      border: #94989a 1px solid;
      border-radius: 3px;

      > div {
        position: absolute;

        &:first-child {
          top: 0;
          width: 0;
          height: 100%;
          background-color: #aad9d2;
        }

        &:last-child {
          top: 0;
          flex-shrink: 0;
          width: 40px;
          height: 40px;
          border-right: #d9d9d9 1px solid;
          color: #666;
          font-size: 18px;
          line-height: 40px;
          text-align: center;
        }
      }
    }
  }
</style>
