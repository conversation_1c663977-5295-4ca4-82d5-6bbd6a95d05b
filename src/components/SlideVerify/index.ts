import SildeVerify from './SlideVerify.vue';
import { createVNode, render } from 'vue';
import { uuid } from '@/utils/uuid';

type SlideVerifyProps = {
  success?: Function;
  fail?: () => void;
  cancel?: () => void;
  el?: HTMLElement;
  close?: () => void; // 添加 close 方法定义
};

class SlideVerifySingleton {
  private static instance: SlideVerifySingleton;
  private div: HTMLElement;
  private isOpen: boolean;

  private constructor() {
    this.div = document.createElement('div');

    this.isOpen = false;
  }

  public static getInstance(): SlideVerifySingleton {
    if (!SlideVerifySingleton.instance) {
      SlideVerifySingleton.instance = new SlideVerifySingleton();
    }
    return SlideVerifySingleton.instance;
  }

  public open(option: SlideVerifyProps) {
    if (!this.isOpen) {
      const uuidStr = uuid('xxxxxxxxxxxxx');
      this.div.setAttribute('class', 'slide-verify' + uuidStr);
      document.body.appendChild(this.div);
      option.el = this.div;
      option.close = () => {
        // 将关闭方法挂载到 option 对象内
        this.close();
      };
      const vnode = createVNode(SildeVerify, option);
      render(vnode, this.div);
      this.isOpen = true;
    }
  }

  private close() {
    if (this.isOpen) {
      console.log('执行关闭方法');
      render(null, this.div);
      document.body.removeChild(this.div);
      this.isOpen = false;
    }
  }
}

export default (option: SlideVerifyProps) => {
  const slideVerify = SlideVerifySingleton.getInstance();
  slideVerify.open(option);
};
