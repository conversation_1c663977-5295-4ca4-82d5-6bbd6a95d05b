import type { App } from 'vue';
import { But<PERSON> } from './Button';
import { Input, Layout, Card, Col, Row, Space, Spin, Empty } from 'ant-design-vue';
import { PageWrapper } from '@/components/Page';
// 部门树组件
// import vue3TreeOrg from 'vue3-tree-org';
// import 'vue3-tree-org/lib/vue3-tree-org.css';

export function registerGlobComp(app: App) {
  app
    .use(Input)
    .use(Button)
    .use(Layout)
    .use(Card)
    .use(Col)
    .use(Row)
    .use(Space)
    .use(PageWrapper)
    // .use(vue3TreeOrg)
    .use(Spin)
    .use(Empty);
}
