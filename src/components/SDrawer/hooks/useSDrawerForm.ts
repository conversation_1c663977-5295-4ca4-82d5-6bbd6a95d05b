import { ref, unref } from 'vue';
import { isProdMode } from '@/utils/env';
import { isEmpty } from 'lodash-es';
import { tryOnUnmounted } from '@vueuse/core';
import {
  DrawerInstance,
  UseDrawerReturnType,
  SDrawerForm,
  OpenDrawerMergeData,
  OpenDrawerData,
} from '../types/SDrawerForm';

export function useSDrawerForm(props: SDrawerForm): UseDrawerReturnType {
  const refSTable = ref<DrawerInstance | null>(null);
  const uid = ref<string | number>('');
  function register(drawerInstance: DrawerInstance, uuid: string | number) {
    isProdMode() &&
      tryOnUnmounted(() => {
        refSTable.value = null;
      });
    uid.value = uuid;
    refSTable.value = drawerInstance;
    drawerInstance.init(props);
  }
  function getInstance() {
    const instance = unref(refSTable);
    if (!instance) {
      console.error('useStable instance is undefined!');
    }
    return instance;
  }
  const methods = {
    addDrawer: (v: OpenDrawerMergeData = {}) => {
      if (isEmpty(v)) {
        getInstance()?.openDrawer({
          isUpdate: false,
        });
      } else {
        getInstance()?.openDrawer(Object.assign(v, { isUpdate: false }) as OpenDrawerData);
      }
    },
    updateDrawer: (v: OpenDrawerMergeData = {}) => {
      getInstance()?.openDrawer(Object.assign(v, { isUpdate: true }) as OpenDrawerData);
    },
  };
  return [register, methods];
}
