import { ref, unref } from 'vue';
import { isProdMode } from '@/utils/env';
import { tryOnUnmounted } from '@vueuse/core';
import {
  DrawerInstance,
  SDrawerDesProps,
  UseSDrawerDesReturnType,
  OpenSDrawerDesData,
} from '../types/SDrawerDes';

export function useSDrawerDes(props: SDrawerDesProps): UseSDrawerDesReturnType {
  const refSDrawerDes = ref<DrawerInstance | null>(null);
  const uid = ref<number>();
  function register(drawerInstance: DrawerInstance, uuid: number) {
    isProdMode() &&
      tryOnUnmounted(() => {
        refSDrawerDes.value = null;
      });
    uid.value = uuid;
    refSDrawerDes.value = drawerInstance;
    drawerInstance.init(props);
  }
  function getInstance() {
    const instance = unref(refSDrawerDes);
    if (!instance) {
      console.error('useStable instance is undefined!');
    }
    return instance;
  }
  const methods = {
    openDrawer: (v: OpenSDrawerDesData = {}) => {
      getInstance()?.openDrawer(v);
    },
  };
  return [register, methods];
}
