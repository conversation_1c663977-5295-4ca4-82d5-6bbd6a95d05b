<template>
  <Drawer
    v-model:open="visibleInner"
    v-bind="$attrs"
    :width="width"
    :closable="closable"
    :rootStyle="{
      position: 'absolute',
    }"
    :content-wrapper-style="{
      maxWidth: '100%',
    }"
    :bodyStyle="{
      padding: 0,
    }"
  >
    <template #extra>
      <slot name="extra"></slot>
    </template>

    <div class="sdrawer-box">
      <slot></slot>
    </div>

    <div class="sdrawer-button">
      <slot name="insertButton" v-bind="propsConfig"></slot>
      <a-button preIcon="ant-design:arrow-left-outlined" v-bind="propsConfig" @click="cancel">
        {{ cancelText }}
      </a-button>
      <slot name="button" v-bind="propsConfig"></slot>
      <a-button :preIcon="okIcon" type="primary" v-bind="propsConfig" @click="ok" v-if="isOk">
        {{ okText }}
      </a-button>
      <slot name="beforeButton" v-bind="propsConfig"></slot>
    </div>
  </Drawer>
</template>

<script lang="ts">
  import { getCurrentInstance, defineComponent, reactive } from 'vue';
  import { Modal, Drawer } from 'ant-design-vue';
  import { DrawerInstance } from '../types/SDrawer';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useVModel } from '@vueuse/core';

  const { t } = useI18n();

  export default defineComponent({
    name: 'SDrawer',
    components: { Drawer },
    props: {
      isOk: {
        type: Boolean,
        default: true,
      },
      cancelText: {
        type: String,
        default: t('component.s_drawer.btn_cancel'),
      },
      okText: {
        type: String,
        default: t('component.s_drawer.btn_confirm'),
      },
      okIcon: {
        type: String,
        default: 'ant-design:check-outlined',
      },
      width: {
        type: String,
        default: '550px',
      },
      closable: {
        type: Boolean,
        default: false,
      },
      loading: {
        type: Boolean,
        default: false,
      },
      visible: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['register', 'onOk', 'onCancel', 'update:open'],
    setup(_props: any, { emit }) {
      const visibleInner = useVModel(_props, 'visible');
      const propsConfig = reactive({
        // 组件默认props
        style: 'min-width: 110px',
        iconSize: 18,
      });
      const instance = getCurrentInstance();
      const sDrawerInstance: DrawerInstance = {
        openDrawer,
        closeDrawer,
        errorDrawer,
      };
      instance && emit('register', sDrawerInstance, instance.uid);

      function openDrawer() {
        emit('update:open', true);
        visibleInner.value = true;
      }
      function closeDrawer() {
        emit('update:open', false);
        visibleInner.value = false;
      }

      function errorDrawer(content = '异常错误，请联系开发者') {
        Modal.error({
          title: '错误',
          content: content,
          onOk() {
            closeDrawer();
          },
          centered: true,
        });
      }
      function ok() {
        emit('onOk');
      }
      function cancel() {
        closeDrawer();
        emit('onCancel');
      }

      return {
        openDrawer,
        closeDrawer,
        propsConfig,
        ok,
        cancel,
        visibleInner,
      };
    },
  });
</script>

<style lang="less" scoped>
  .sdrawer {
    position: absolute;
    // visibility: hidden;
    z-index: 100;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    transition: all 0.3s;
    border-left: #dfdfdf solid 1px;
    // background: #546b8e;
    background: #f0f2f5;

    &-box {
      width: 100%;
      height: 100%;
      padding-bottom: 80px;
      overflow: auto;
    }

    &-button {
      display: flex;
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 60px;
      padding: 0 20px;
      border-top: #eee8 solid 1px;
      background-color: @component-background-alpha;
      box-shadow:
        0 -6px 16px -8px rgb(0 0 0 / 4%),
        0 -9px 28px 0 rgb(0 0 0 / 3%),
        0 -12px 48px 16px rgb(0 0 0 / 2%);
      gap: 15px;
      // 背景模糊
      backdrop-filter: blur(2px);
    }
  }
</style>
