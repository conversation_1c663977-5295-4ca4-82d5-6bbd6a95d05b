<template>
  <Drawer
    v-model:open="visible"
    :bodyStyle="{ padding: 0 }"
    :rootStyle="{ position: 'absolute' }"
    :content-wrapper-style="{ maxWidth: '100%' }"
    v-bind="$attrs"
    :width="width"
    :closable="closable"
    :destroyOnClose="propsConfig.destroyOnClose"
    :title="getTitle"
  >
    <!-- 表单区域 -->
    <div class="sdrawerForm-box">
      <div>
        <div class="px-35px pt-30px pb-40px flex items-center justify-center flex-col">
          <BasicForm class="max-w-800px w-100%" @register="registerForm" @submit="onSubmit" />
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="sdrawerForm-button">
      <a-button
        preIcon="ant-design:arrow-left-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        size="default"
        @click="closeDrawer"
      >
        {{ t('component.s_drawer_form.btn_cancel') }}
      </a-button>

      <a-button
        preIcon="ant-design:check-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        size="default"
        :loading="loading"
        type="primary"
        @click="handleSubmit"
      >
        {{ t('component.s_drawer_form.btn_save') }}
      </a-button>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, unref, getCurrentInstance, nextTick, toRaw } from 'vue';
  import { message, Drawer } from 'ant-design-vue';
  import { isEmpty, isPlainObject, isFunction, cloneDeep, omit } from 'lodash-es';

  import { BasicForm, useForm } from '@/components/Form/index';
  import { useI18n } from '@/hooks/web/useI18n';

  import type { DrawerInstance, OpenDrawerData, SDrawerForm } from '../types/SDrawerForm';

  /* ------------------------------------------------------------------
   * props / emits
   * ---------------------------------------------------------------- */
  interface Props {
    width?: string;
    closable?: boolean;
  }

  /* 默认值写在 withDefaults，保持响应式一致 */
  withDefaults(defineProps<Props>(), {
    width: '550px',
    closable: false,
  });

  const emit = defineEmits<{
    (e: 'register', inst: DrawerInstance, uid: number): void;
    (e: 'success', payload: { isUpdate: boolean; value: any }): void;
  }>();

  /* ------------------------------------------------------------------
   * 多语言
   * ---------------------------------------------------------------- */
  const { t } = useI18n();

  /* ------------------------------------------------------------------
   * 内部状态
   * ---------------------------------------------------------------- */
  const visible = ref(false);
  const loading = ref(false);
  const isUpdate = ref(false);
  let merge: any = undefined; // 用于表单合并 （可选） openDrawer 时传入
  let defaultMerge: any = undefined; // 用于表单合并 （可选） useSDrawerForm 时传入

  let registerForm!: any; // 用于 BasicForm 注册
  let formMethod!: any; // BasicForm 方法
  let addFn!: ((v: any) => Promise<any>) | Function | undefined;
  let updateFn!: ((v: any) => Promise<any>) | Function | undefined;

  /* ------------------------------------------------------------------
   * 组件配置（可被 init 动态合并）
   * ---------------------------------------------------------------- */
  const propsConfig = reactive<SDrawerForm>({
    layout: 'vertical',
    autoFocusFirstItem: true,
    baseColProps: { span: 24 },
    showActionButtonGroup: false,
    buttonStyle: 'min-width: 110px',
    addText: t('component.s_drawer_form.title_add'),
    updateText: t('component.s_drawer_form.title_edit'),
    destroyOnClose: false,
    // 回车提交
    autoSubmitOnEnter: true,
  });

  /* 标题自动切换 */
  const getTitle = computed(() => (unref(isUpdate) ? propsConfig.updateText : propsConfig.addText));

  /* ------------------------------------------------------------------
   * 方法
   * ---------------------------------------------------------------- */
  function openDrawer(data: OpenDrawerData) {
    visible.value = true;

    const { isUpdate: flag, record, beforeFn, title } = data;
    merge = data.merge;
    isUpdate.value = flag;

    /* 动态自定义标题（可选） */
    if (title) {
      flag ? (propsConfig.updateText = title) : (propsConfig.addText = title);
    }

    /* 表单重置 + 预处理 */
    nextTick(async () => {
      try {
        await formMethod.resetFields();
        if (beforeFn) await beforeFn(formMethod, data);
        if (!isEmpty(record)) formMethod.setFieldsValue(record);
      } catch (err) {
        console.error(err);
      }
    });
  }

  async function handleSubmit() {
    if (!formMethod?.submit) {
      message.error('formMethod.submit 不存在，请联系开发者');
      return;
    }
    try {
      await formMethod.submit();
    } catch {
      message.error(t('component.s_drawer_form.msg_error_from'));
    }
  }

  async function onSubmit(values: any) {
    try {
      loading.value = true;
      let result = cloneDeep(values);
      /* 默认 merge 处理 */
      if (defaultMerge && isPlainObject(defaultMerge)) {
        Object.assign(result, defaultMerge);
      } else if (isFunction(defaultMerge)) {
        result = (await defaultMerge(result)) || {};
      }

      /* merge 处理 */
      if (merge && isPlainObject(merge)) {
        Object.assign(result, merge);
      } else if (isFunction(merge)) {
        result = (await merge(result)) || {};
      }

      /* add / update */
      if (unref(isUpdate)) {
        updateFn && (await updateFn(result));
      } else {
        addFn && (await addFn(result));
      }

      closeDrawer();
      emit('success', { isUpdate: unref(isUpdate), value: result });
    } catch (err) {
      console.error(err);
    } finally {
      loading.value = false;
    }
  }

  function closeDrawer() {
    visible.value = false;
  }

  /* 供外部初始化表单配置 */
  function init(config: SDrawerForm) {
    addFn = config.addFn;
    updateFn = config.updateFn;
    defaultMerge = config.merge; // 用于表单合并 （可选） useSDrawerForm 时传入
    Object.assign(propsConfig, omit(config, ['merge', ' addFn', 'updateFn']));
    // ❗ 关键：避免把 Proxy 直接喂给 useForm
    const rawCfg = toRaw(propsConfig) as SDrawerForm;
    /* 获取 BasicForm 注册函数 与 方法 */
    [registerForm, formMethod] = useForm(rawCfg);
  }

  /* ------------------------------------------------------------------
   * 向父级暴露实例（保持原有 API）
   * ---------------------------------------------------------------- */
  const instance = getCurrentInstance();
  if (instance) {
    const sDrawerInstance: DrawerInstance = { openDrawer, init };
    emit('register', sDrawerInstance, instance.uid);
  }

  /* ------------------------------------------------------------------
   * 让 <script setup> 之外仍可通过 ref 获取实例方法
   * ---------------------------------------------------------------- */
  defineExpose({ openDrawer, init });
</script>

<style lang="less" scoped>
  .sdrawerForm {
    &-box {
      width: 100%;
      height: 100%;
      padding-bottom: 80px;
      overflow: auto;
    }

    &-button {
      display: flex;
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 60px;
      padding: 0 20px;
      gap: 15px;
      border-top: #eee8 solid 1px;
      background-color: @component-background-alpha;
      box-shadow:
        0 -6px 16px -8px rgb(0 0 0 / 4%),
        0 -9px 28px 0 rgb(0 0 0 / 3%),
        0 -12px 48px 16px rgb(0 0 0 / 2%);
      backdrop-filter: blur(2px);
    }
  }
</style>
