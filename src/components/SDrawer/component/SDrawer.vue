<template>
  <Drawer
    v-model:open="openInner"
    v-bind="$attrs"
    :width="width"
    :closable="closable"
    :rootStyle="{ position: 'absolute' }"
    :content-wrapper-style="{ maxWidth: '100%' }"
    :bodyStyle="{ padding: 0 }"
  >
    <!-- 顶部右侧插槽 -->
    <template #extra>
      <slot name="extra"></slot>
    </template>

    <!-- 主体内容 -->
    <div class="sdrawer-box">
      <slot></slot>
    </div>

    <!-- 底部操作区 -->
    <div class="sdrawer-button">
      <slot name="insertButton" v-bind="propsConfig"></slot>

      <a-button preIcon="ant-design:arrow-left-outlined" v-bind="propsConfig" @click="cancel">
        {{ cancelText }}
      </a-button>

      <slot name="button" v-bind="propsConfig"></slot>

      <a-button v-if="isOk" :preIcon="okIcon" type="primary" v-bind="propsConfig" @click="ok">
        {{ okText }}
      </a-button>

      <slot name="beforeButton" v-bind="propsConfig"></slot>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
  import { reactive, getCurrentInstance } from 'vue';
  import { Drawer, Modal } from 'ant-design-vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useVModel } from '@vueuse/core';
  import type { DrawerInstance } from '../types/SDrawer';

  /* ----------------------------------------------------
   * props & emits
   * --------------------------------------------------*/
  interface Props {
    isOk?: boolean;
    cancelText?: string;
    okText?: string;
    okIcon?: string;
    width?: string;
    closable?: boolean;
    loading?: boolean;
    open?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    isOk: true,
    cancelText: () => {
      return useI18n().t('component.s_drawer.btn_cancel');
    },
    okText: () => {
      return useI18n().t('component.s_drawer.btn_confirm');
    },
    okIcon: 'ant-design:check-outlined',
    width: '550px',
    closable: false,
    loading: false,
    open: false,
  });

  const emit = defineEmits<{
    (e: 'register', inst: DrawerInstance, uid: number): void;
    (e: 'onOk'): void;
    (e: 'onCancel'): void;
    (e: 'update:open', value: boolean): void;
  }>();

  /* ----------------------------------------------------
   * 受控可见性（结合 v-model:open）
   * --------------------------------------------------*/
  const openInner = useVModel(props, 'open', emit);

  /* ----------------------------------------------------
   * 公共按钮配置
   * --------------------------------------------------*/
  const propsConfig = reactive({
    style: 'min-width: 110px',
    iconSize: 18,
  });

  /* ----------------------------------------------------
   * drawer 操作方法
   * --------------------------------------------------*/
  function openDrawer() {
    emit('update:open', true);
    openInner.value = true;
  }

  function closeDrawer() {
    emit('update:open', false);
    openInner.value = false;
  }

  function errorDrawer(content = '异常错误，请联系开发者') {
    Modal.error({
      title: '错误',
      content,
      centered: true,
      onOk: closeDrawer,
    });
  }

  function ok() {
    emit('onOk');
  }

  function cancel() {
    closeDrawer();
    emit('onCancel');
  }

  /* ----------------------------------------------------
   * 把实例方法通过 register 暴露给父级
   * --------------------------------------------------*/
  const instance = getCurrentInstance();
  if (instance) {
    const sDrawerInstance: DrawerInstance = {
      openDrawer,
      closeDrawer,
      errorDrawer,
    };
    emit('register', sDrawerInstance, instance.uid);
  }

  /* ----------------------------------------------------
   * （可选）对外暴露方法，便于 <script setup> 的引用
   * --------------------------------------------------*/
  defineExpose({ openDrawer, closeDrawer, errorDrawer });
</script>

<style lang="less" scoped>
  .sdrawer {
    position: absolute;
    // visibility: hidden;
    z-index: 100;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    transition: all 0.3s;
    border-left: #dfdfdf solid 1px;
    // background: #546b8e;
    background: #f0f2f5;

    &-box {
      width: 100%;
      height: 100%;
      padding-bottom: 80px;
      overflow: auto;
    }

    &-button {
      display: flex;
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 60px;
      padding: 0 20px;
      border-top: #eee8 solid 1px;
      background-color: @component-background-alpha;
      box-shadow:
        0 -6px 16px -8px rgb(0 0 0 / 4%),
        0 -9px 28px 0 rgb(0 0 0 / 3%),
        0 -12px 48px 16px rgb(0 0 0 / 2%);
      gap: 15px;
      // 背景模糊
      backdrop-filter: blur(2px);
    }
  }
</style>
