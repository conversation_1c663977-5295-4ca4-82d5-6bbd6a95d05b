<template>
  <Drawer
    v-model:open="visible"
    :bodyStyle="{ padding: 0 }"
    :rootStyle="{ position: 'absolute' }"
    :content-wrapper-style="{ maxWidth: '100%' }"
    v-bind="$attrs"
    :width="width"
    :closable="closable"
    :destroyOnClose="propsConfig.destroyOnClose"
    :title="getTitle"
  >
    <!-- 表单区域 -->
    <div class="sdrawerFormDes-box">
      <div class="px-25px pt-15px pb-40px flex justify-center flex-col">
        <Description @register="registerDesc" />
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="sdrawerFormDes-button">
      <a-button
        preIcon="ant-design:arrow-left-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        size="default"
        @click="closeDrawer"
      >
        {{ t('component.s_drawer_form.btn_back') }}
      </a-button>
    </div>
  </Drawer>
</template>

<script setup lang="ts">
  import { ref, reactive, getCurrentInstance, nextTick, toRaw } from 'vue';
  import { Drawer } from 'ant-design-vue';
  import { Description, useDescription } from '@/components/Description/index';
  import { useI18n } from '@/hooks/web/useI18n';

  import type { DrawerInstance, OpenSDrawerDesData, SDrawerDesProps } from '../types/SDrawerDes';
  import { omit } from 'lodash-es';

  /* ------------------------------------------------------------------
   * props / emits
   * ---------------------------------------------------------------- */
  interface Props {
    width?: string;
    closable?: boolean;
  }

  /* 默认值写在 withDefaults，保持响应式一致 */
  withDefaults(defineProps<Props>(), {
    width: '550px',
    closable: false,
  });

  const emit = defineEmits<{
    (e: 'register', inst: DrawerInstance, uid: number): void;
  }>();

  /* ------------------------------------------------------------------
   * 多语言
   * ---------------------------------------------------------------- */
  const { t } = useI18n();

  /* ------------------------------------------------------------------
   * 内部状态
   * ---------------------------------------------------------------- */
  const visible = ref(false);

  let registerDesc: any;
  let descMethod: any;

  /* ------------------------------------------------------------------
   * 组件配置（可被 init 动态合并）
   * ---------------------------------------------------------------- */
  const propsConfig = reactive<SDrawerDesProps>({
    schema: [],
    layout: 'horizontal',
    buttonStyle: {
      minWidth: '110px',
    },
    title: t('component.s_drawer_form.title'),
    destroyOnClose: false,
    bordered: false,
    column: 1,
  });
  const descData = ref<Record<string, any>>({});
  /* 标题自动切换 */
  const getTitle = ref(propsConfig.title);

  /* ------------------------------------------------------------------
   * 方法
   * ---------------------------------------------------------------- */
  function openDrawer(data: OpenSDrawerDesData) {
    visible.value = true;

    const { record, title } = data;

    /* 动态自定义标题（可选） */
    if (title) {
      getTitle.value = title;
    } else {
      getTitle.value = propsConfig.title; // 默认标题
    }
    descData.value = record || {}; // 初始化数据
    // 等待下一帧，以确保 Description 组件已渲染
    nextTick(() => descMethod?.setDescProps?.({ data: descData.value }));
  }

  function closeDrawer() {
    visible.value = false;
  }

  /* 供外部初始化表单配置 */
  function init(config: SDrawerDesProps) {
    Object.assign(propsConfig, config);
    // ❗ 关键：避免把 Proxy 直接喂给 useForm
    const rawCfg = toRaw(propsConfig) as unknown as SDrawerDesProps;
    // 合并配置并初始化 useDescription
    [registerDesc, descMethod] = useDescription({
      ...omit(rawCfg, ['title']),
      data: descData,
    });
  }

  /* ------------------------------------------------------------------
   * 向父级暴露实例（保持原有 API）
   * ---------------------------------------------------------------- */
  const instance = getCurrentInstance();
  if (instance) {
    const sDrawerInstance: DrawerInstance = { openDrawer, init };
    emit('register', sDrawerInstance, instance.uid);
  }

  /* ------------------------------------------------------------------
   * 让 <script setup> 之外仍可通过 ref 获取实例方法
   * ---------------------------------------------------------------- */
  defineExpose({ openDrawer, init });
</script>

<style lang="less" scoped>
  .sdrawerFormDes {
    &-box {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding-bottom: 80px;
      overflow: auto;
    }

    &-button {
      display: flex;
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 60px;
      padding: 0 20px;
      gap: 15px;
      border-top: #eee8 solid 1px;
      background-color: @component-background-alpha;
      box-shadow:
        0 -6px 16px -8px rgb(0 0 0 / 4%),
        0 -9px 28px 0 rgb(0 0 0 / 3%),
        0 -12px 48px 16px rgb(0 0 0 / 2%);
      backdrop-filter: blur(2px);
    }
  }
</style>
