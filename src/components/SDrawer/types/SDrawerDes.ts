import type { DescriptionsProps } from 'ant-design-vue/es/descriptions/index';
import type { DescItem } from '@/components/Description/src/typing';
import type { CSSProperties } from 'vue';

export interface DrawerInstance {
  openDrawer: (OpenDrawerData) => void;
  init: (props) => void;
}

export type RegisterFn = (drawerInstance: DrawerInstance, uuid: number) => void;

/**SDrawerFormDes */
export interface SDrawerDesProps extends DescriptionsProps {
  buttonStyle?: CSSProperties;
  title?: string;
  destroyOnClose?: boolean;
  schema: DescItem[];
  record?: Recordable;
}

export interface OpenSDrawerDesData {
  record?: any;
  title?: string;
}

export interface DesMethods {
  openDrawer: (record?: OpenSDrawerDesData) => void;
}
export type UseSDrawerDesReturnType = [RegisterFn, DesMethods];
