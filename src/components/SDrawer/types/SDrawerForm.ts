import { FormProps, FormActionType } from '@/components/Form/src/types/form';

export interface DrawerInstance {
  openDrawer: (OpenDrawerData) => void;
  init: (props) => void;
}

export interface mergeObj {
  [key: string]: string | boolean | number | Array<string> | null | undefined;
}
type merge = mergeObj | Function;
export interface OpenDrawerData {
  isUpdate: boolean;
  record?: any;
  merge?: merge;
  beforeFn?: beforeFn;
  title?: string;
}

//定义一个type，描述方法beforeFn
type beforeFn = (action: FormActionType, data: OpenDrawerData) => void;

export interface OpenDrawerMergeData {
  record?: any;
  merge?: merge;
  beforeFn?: beforeFn;
  title?: string;
}
export interface SDrawerForm extends FormProps {
  addFn?: Function;
  updateFn?: Function;
  saveButtonText?: string;
  backButtonText?: string;
  buttonStyle?: string;
  addText?: string;
  merge?: merge;
  updateText?: string;
  destroyOnClose?: boolean;
}

export interface methods {
  addDrawer: (record?: OpenDrawerMergeData) => void;
  updateDrawer: (record: OpenDrawerMergeData) => void;
}
export type RegisterFn = (drawerInstance: DrawerInstance, uuid: string | number) => void;

export type UseDrawerReturnType = [RegisterFn, methods];

export interface methodsInner {
  closeDrawer: () => void;
  setLoading: (arg0: boolean) => void;
  errorDrawer: (arg0?: string) => void;
}
export type UseDrawerInnerReturnType = [RegisterFn, methodsInner];
