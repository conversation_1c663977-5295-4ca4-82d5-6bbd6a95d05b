export interface DrawerInstance {
  openDrawer: () => void;
  closeDrawer: () => void;
  errorDrawer: (arg0?: string) => void;
}
export interface methods {
  openDrawer: (data?: any) => void;
  closeDrawer: () => void;
}
export type RegisterFn = (drawerInstance: DrawerInstance, uuid: string) => void;

export type UseDrawerReturnType = [RegisterFn, methods];

export interface methodsInner {
  closeDrawer: () => void;
  errorDrawer: (arg0?: string) => void;
}
export type UseDrawerInnerReturnType = [RegisterFn, methodsInner];
