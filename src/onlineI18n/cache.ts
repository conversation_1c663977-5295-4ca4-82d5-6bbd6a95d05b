import { useLocaleStoreWithOut } from '@/store/modules/locale';
import { apiGetI18nByCodeList } from '@/api/admin/i18n';
import { type Ref, ref } from 'vue';
import { throttle } from 'lodash-es';

// 翻译缓存项接口
export interface TranslationCacheItem {
  value: Ref<string>;
  timestamp: number;
  loading: Ref<boolean>;
  success: Ref<boolean>;
}

// 请求队列结构
interface RequestQueueItem {
  queueId: string;
  codes: string[];
  resolve: (value: string[]) => void;
  reject: (reason?: any) => void;
  // 是否开始请求
  isFetching: boolean;
}

//在线 i18n 缓存 类 这个类用于存储翻译缓存 和获取翻译
export class OnlineI18nCache {
  private static instance: OnlineI18nCache;
  // 翻译缓存
  private translationCache: Map<string, TranslationCacheItem> = new Map();

  // 正在加载的翻译
  private pendingCodes: Set<string> = new Set();

  // 请求队列
  private pendingQueue: Map<string, RequestQueueItem> = new Map();

  // 加载失败的翻译 目前只用来记录
  private failedCodes: Set<string> = new Set();

  private constructor() {}

  public static getInstance(): OnlineI18nCache {
    if (!OnlineI18nCache.instance) {
      OnlineI18nCache.instance = new OnlineI18nCache();
    }
    return OnlineI18nCache.instance;
  }
  // 请求翻译 第一个参数是语言代码，第二个参数是请求队列 ids
  public doFetch = async (codes: string[]) => {
    // 获取当前语言
    const localeStore = useLocaleStoreWithOut();
    const currentLocale = localeStore.getLocale;
    try {
      // 设置正在加载的翻译
      codes.forEach((code) => {
        this.pendingCodes.add(code); // 设置正在加载的翻译
      });

      const translations = await apiGetI18nByCodeList({
        codes,
        locale: currentLocale,
      });

      translations.forEach(({ code, value }) => {
        // 查找翻译缓存
        // 拼接语言
        const cacheKey = `${currentLocale}_${code}`;
        const cacheItem = this.translationCache.get(cacheKey);
        if (cacheItem) {
          // 更新翻译
          cacheItem.value.value = value;
          cacheItem.loading.value = false;
          cacheItem.success.value = true;
          cacheItem.timestamp = Date.now();
        }
      });
      // 至此，翻译已经加载完成，从 pendingCodes 中移除

      // 获取 translations 中没有的 code
      const missingCodes = codes.filter((code) => !translations.find((item) => item.code === code));
      // 更新没有的翻译的 loading 为 false
      missingCodes.forEach((code) => {
        const cacheKey = `${currentLocale}_${code}`;
        const cacheItem = this.translationCache.get(cacheKey);
        if (cacheItem) {
          cacheItem.loading.value = false;
          cacheItem.success.value = false;
        }
      });
      // 设置为失败
      missingCodes.forEach((code) => {
        this.failedCodes.add(code);
      });
    } catch (error) {
      console.error('Failed to fetch translations:', error);
    } finally {
      // 移除正在加载的翻译
      codes.forEach((code) => {
        this.pendingCodes.delete(code);
      });
    }
  };

  private runTask = async () => {
    // 获取需要请求的队列
    const queue = Array.from(this.pendingQueue.values());
    if (queue.length === 0) {
      return;
    }
    // 列出需要请求的 queueId isFetching 为 false 的
    const queueIds = queue.filter((item) => !item.isFetching).map((item) => item.queueId);
    // 标记为正在请求
    queueIds.forEach((queueId) => {
      const item = this.pendingQueue.get(queueId);
      if (item) {
        item.isFetching = true;
      }
    });
    // 根据 queueId 获取需要请求的 code
    const codes = Array.from(queueIds)
      .map((queueId) => {
        const item = this.pendingQueue.get(queueId);
        if (item) {
          return item.codes;
        }
        return [];
      })
      .flat();
    try {
      await this.doFetch(codes); // 请求翻译
      // 处理请求队列
      queueIds.forEach((queueId) => {
        const item = this.pendingQueue.get(queueId);
        if (item) {
          item.resolve(item.codes);
          this.pendingQueue.delete(queueId);
        }
      });
    } catch (error) {
      console.error('Failed to run task:', error);
      // 处理请求队列
      queueIds.forEach((queueId) => {
        const item = this.pendingQueue.get(queueId);
        if (item) {
          item.reject(error);
          this.pendingQueue.delete(queueId);
        }
      });
    }
  };

  // 封装防抖函数
  private throttleRunTask = throttle(this.runTask.bind(this), 50, {
    leading: false, // 不立即执行
    trailing: true, // 结束后执行一次
  });
  // 单个获取翻译
  public getTranslation = (code: string, defaultValue: string = '') => {
    // 获取当前语言
    const localeStore = useLocaleStoreWithOut();
    const currentLocale = localeStore.getLocale;
    // 拼接语言
    const cacheKey = `${currentLocale}_${code}`;
    // 查找翻译缓存
    const cacheItem = this.translationCache.get(cacheKey);
    if (cacheItem) {
      // 如果缓存存在，判断是否是 loading  状态 如果是 loading 状态，则返回缓存项
      if (cacheItem.loading.value || cacheItem.success.value) {
        return cacheItem;
      }
    } else {
      // 如果缓存不存在，创建一个新的缓存项
      const newCacheItem: TranslationCacheItem = {
        value: ref(defaultValue),
        timestamp: Date.now(),
        loading: ref(true),
        success: ref(false),
      };
      this.translationCache.set(cacheKey, newCacheItem);
    }

    // 生成一个唯一的队列 id
    const queueId = Math.random().toString(36).substring(2, 11);
    // 将请求加入队列

    // 创建 promise 对象
    void new Promise<string[]>((resolve, reject) => {
      // 将请求加入队列
      this.pendingQueue.set(queueId, {
        queueId,
        codes: [code],
        resolve,
        reject,
        isFetching: false,
      });
      // 执行任务
      this.throttleRunTask(); // 执行任务
    }).catch(() => {});
    // 不需要返回 promise
    // 返回创建的缓存项
    return this.translationCache.get(cacheKey)!;
  };

  // 批量翻译预请求 数据结构支持 {code: string, defaultValue: string}[] 或者 string[]
  public getTranslations = async (codes: string[] | { code: string; defaultValue: string }[]) => {
    const localeStore = useLocaleStoreWithOut();
    const currentLocale = localeStore.getLocale;
    // 先判断这些 code 是否已经存在 请求结果 loading false ,success false 需要重新请求
    const codeList = codes
      .map((item: string | { code: string; defaultValue: string }) => {
        if (typeof item === 'string') {
          return { code: item, defaultValue: '' };
        }
        return item;
      })
      .filter((item) => {
        const cacheKey = `${currentLocale}_${item.code}`;
        const cacheItem = this.translationCache.get(cacheKey);
        if (cacheItem) {
          if (cacheItem.loading.value || cacheItem.success.value) {
            return false;
          }
        }
        // 创建一个新的缓存项
        const newCacheItem: TranslationCacheItem = {
          value: ref(item.defaultValue),
          timestamp: Date.now(),
          loading: ref(true),
          success: ref(false),
        };
        this.translationCache.set(cacheKey, newCacheItem);
        return true;
      });
    // 生成一个唯一的队列 id
    const queueId = Math.random().toString(36).substring(2, 11);

    // 将请求加入队列
    void new Promise<string[]>((resolve, reject) => {
      this.pendingQueue.set(queueId, {
        queueId,
        codes: codeList.map((item) => item.code),
        resolve,
        reject,
        isFetching: false,
      });
      // 执行任务
      this.throttleRunTask(); // 执行任务
    }).catch(() => {});
    // 不需要返回 promise
  };
}
