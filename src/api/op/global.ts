import { defHttp } from '@/utils/http/axios';
import type { GlobalLocaltion } from './model/global';

enum Api {
  GlobalPageInfo = '/admin/sys/global/pageInfo',
  Global = '/admin/sys/global',
  GlobalLocaltion = '/admin/sys/global/locationByPid',
}

/** 分页查询 */
export const apiGetGlobalPageInfo = (params: any) =>
  defHttp.get({ url: Api.GlobalPageInfo, params });

/** 添加 */
export const apiPostGlobal = (data: any) => defHttp.post({ url: Api.Global, data });

/** 修改 */
export const apiPutGlobal = (data: any) => defHttp.put({ url: Api.Global, data });

/** 获取城市信息 */
export const apiGetGlobalLocaltion = (pid: any): Promise<GlobalLocaltion> =>
  defHttp.get({ url: Api.GlobalLocaltion, params: { pid } });
