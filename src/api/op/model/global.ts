export interface GlobalLocaltion {
  id: number; // 主键
  parentId: number; // 父类id
  level: number; // 等级
  path: string; // 路径
  code: string; // 代码
  languageCode: string; // 语言编码
  countryNumber: string; // 国家区号
  phoneNumber: string; // 国家电话区号
  abbreviation: string; // 国家缩写
  iso: string; // 时区
  chineseName: string; // 中文名称
  foreignName: string; // 外文名称
  pathChinese: string; // 路径中文名
  pathForeign: string; // 路径外语名
  pinyin: string; // 拼音
}
