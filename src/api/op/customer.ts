import { defHttp } from '@/utils/http/axios';
import type { AxiosRequestConfig } from 'axios';

enum Api {
  PageInfo = '/admin/sys/customer/pageInfo',
  Create = '/admin/sys/customer/create',
  Customer = '/admin/sys/customer',
  CustomerInfo = '/admin/sys/customer/info/{id}',
  Change = '/admin/sys/customer/change/{id}/{state}', // 修改状态
  Export = '/admin/sys/customer/export',
  BatchDelete = '/admin/sys/customer/batchDelete/{type}',
  Import = '/admin/sys/customer/import',
  Template = '/admin/sys/customer/template',
}

/**
 * @description: 获取客户分页列表
 */
export const apiGetCustomerPage = (params: any) => {
  return defHttp.get({ url: Api.PageInfo, params });
};

/**
 * @description: 获取客户列表（不分页）
 */
export const apiGetCustomerList = (params?: any) => {
  return defHttp.get({ url: Api.Customer, params });
};

/**
 * @description: 新增客户
 */
export const apiAddCustomer = (data: any) => {
  return defHttp.post(
    { url: Api.Create, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 获取客户详情（通过ID）
 */
export const apiGetCustomerById = (id: string) => {
  return defHttp.get({ url: Api.CustomerInfo.replace('{id}', id) });
};

/**
 * @description: 更新客户信息
 */
export const apiUpdateCustomer = (data: any) => {
  return defHttp.put(
    { url: Api.Customer, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 删除客户
 */
export const apiDeleteCustomer = (id: string) => {
  return defHttp.delete(
    { url: `${Api.Customer}/${id}` },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 批量删除客户
 */
export const apiBatchDeleteCustomer = (ids: string[], type: string = '0') => {
  return defHttp.delete(
    { url: Api.BatchDelete.replace('{type}', type), data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 修改客户状态
 */
export const apiChangeCustomerState = (id: string, state: boolean) => {
  return defHttp.put(
    { url: Api.Change.replace('{id}', id).replace('{state}', state.toString()) },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 导出客户数据
 */
export const apiExportCustomer = (params: any) => {
  return defHttp.get(
    { url: Api.Export, params, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description: 导入客户数据
 */
export const apiImportCustomer = (params: any, config: AxiosRequestConfig) => {
  return defHttp.uploadFile({ url: Api.Import, ...config }, params);
};

/**
 * @description: 下载客户导入模板
 */
export const apiDownloadCustomerTemplate = () => {
  return defHttp.get(
    { url: Api.Template, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );
};
