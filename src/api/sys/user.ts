import { defHttp } from '@/utils/http/axios';
import { LoginParams, LoginResultModel, GetUserInfoModel, GetVerifyModel } from './model/userModel';
import { encryption } from '@/utils/cipher';
import { ErrorMessageMode } from '#/axios';
import { getClient } from '@/settings/clientSetting';
import { ContentTypeEnum } from '@/enums/httpEnum';
import { get } from 'lodash-es';

enum Api {
  Login = '/admin/oauth2/token',
  Logout = '/admin/token/logout',
  GetUserInfo = '/admin/user/info',
  GetPermCode = '/getPermCode',
  TestRetry = '/testRetry',
  GetVerify = '/admin/code/create',
  ReqCheck = '/admin/code/check',
  UserChangePass = '/admin/sys/user/password',
}

/**
 * @description: user login api
 */
export async function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  const client = getClient(params.clientId)!;
  const { username, code, randomStr, password, tenantId } = encryption({
    key: client.aesKey,
    param: ['password'],
    data: params,
  });
  const dataObj = { username: username, password: password };
  try {
    return await defHttp.post<LoginResultModel>(
      {
        url: Api.Login,
        data: dataObj,
        params: { randomStr, code, grant_type: 'password', scope: client.scope },
        headers: {
          'TENANT-ID': tenantId,
          Authorization: client.Authorization,
          'Content-Type': ContentTypeEnum.FORM_URLENCODED,
        },
      },
      {
        errorMessageMode: mode,
        isTransformResponse: false,
      },
    );
  } catch (error) {
    // 获取错误信息
    const errMessage = get(error, 'response.data.msg') || '登录失败';
    if (errMessage) {
      throw new Error(errMessage);
    }
  }
}

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.get<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' });
}

export function getPermCode() {
  return defHttp.get<string[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return defHttp.delete({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    },
  );
}

export function getVerify() {
  return defHttp.get<GetVerifyModel>({ url: Api.GetVerify });
}
export function reqCheck(params) {
  return defHttp.post({ url: Api.ReqCheck, params });
}

/**
 * @description: 修改当前用户密码
 */
export function userChangePass(data: any) {
  return defHttp.put(
    {
      url: Api.UserChangePass,
      data,
    },
    { successMessageMode: 'message' },
  );
}
