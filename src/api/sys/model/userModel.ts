/**
 * @description: Login interface parameters
 */
export interface LoginParams {
  username: string;
  password: string;
  code: string;
  randomStr: string;
  clientId: string;
  tenantId: string;
}

export interface RoleInfo {
  roleName: string;
  value: string;
}

/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  // token
  access_token: string;
  // 是否是租户管理员
  active: boolean;
  // client 信息
  client_id: string;
  // 过期时间
  expires_in: number;
  // 刷新token
  refresh_token: string;
  // token类型
  token_type: string;
  // 用户id
  user_id: string;
  // 用户名
  username: string;
  // 许可
  license: string;
}

/**
 * @description: Get user information return value
 */
export interface GetUserInfoModel {
  sysUser: SysUser;
  permissions: string[];
  roles: string[];
}

interface SysUser {
  userId: string;
  username: string;
  password: string | null;
  createBy: string;
  updateBy: string;
  createTime: string;
  updateTime: string;
  delFlag: string;
  lockFlag: string;
  passwordExpireFlag: string;
  passwordModifyTime: string;
  phone: string;
  avatar: string;
  deptId: string;
  tenantId: string;
  wxOpenid: string | null;
  wxCpUserid: string | null;
  wxDingUserid: string | null;
  miniOpenid: string | null;
  qqOpenid: string | null;
  giteeLogin: string | null;
  oscId: string | null;
  nickname: string;
  name: string;
  email: string;
  homePath?: string;
}

export interface GetVerifyModel {
  repCode: string;
  repData: GetVerifyModelRepData;
  repMsg: string | null;
  success: boolean;
}

export interface GetVerifyModelRepData {
  browserInfo: string | null;
  captchaFontSize: string | null;
  captchaFontType: string | null;
  captchaId: number | null;
  captchaOriginalPath: string | null;
  captchaType: string;
  captchaVerification: string;
  clientUid: string;
  jigsawImageBase64: string;
  originalImageBase64: string;
  point: string;
  pointJson: string;
  pointList: Array<string>;
  projectCode: string;
  result: boolean;
  secretKey: string;
  token: string;
  ts: string;
  wordList: Array<string>;
}
