import { defHttp } from '@/utils/http/axios';

enum Api {
  Social = '/admin/social',
  SocialPage = '/admin/social/page',
  SocialDetails = '/admin/social/getById',
  SocialLoginAppList = '/admin/social/getLoginAppList',
}

/**
 * @description: 获取社交登录分页列表
 */
export const apiGetSocialPage = (params: any) => {
  return defHttp.get({ url: Api.SocialPage, params });
};

/**
 * @description: 新增社交登录配置
 */
export const apiAddSocial = (data: any) => {
  return defHttp.post(
    { url: Api.Social, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 获取社交登录详情（通过ID）
 */
export const apiGetSocialById = (id: string) => {
  return defHttp.get({ url: `${Api.SocialDetails}/${id}` });
};

/**
 * @description: 删除社交登录配置
 */
export const apiDeleteSocial = (ids: string[]) => {
  return defHttp.delete(
    { url: Api.Social, data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 更新社交登录配置
 */
export const apiUpdateSocial = (data: any) => {
  return defHttp.put(
    { url: Api.Social, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 获取登录应用列表
 */
export const apiGetSocialLoginAppList = () => {
  return defHttp.get({ url: Api.SocialLoginAppList });
};
