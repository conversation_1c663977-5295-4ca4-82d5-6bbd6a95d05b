import { defHttp } from '@/utils/http/axios';
import { getMenuListResultModel } from './model/menuModel';

enum Api {
  MenuTree = '/admin/menu/tree',
  Menu = '/admin/menu',
  MenuDetails = '/admin/menu/details',
}

/**
 * 获取菜单树
 */
export const apiGetMenuTree = (params?: any) => {
  return defHttp.get({ url: Api.MenuTree, params });
};

/**
 * 获取登录用的菜单列表
 */
export const apiGetMenuList = () => {
  return defHttp.get<getMenuListResultModel>({ url: Api.Menu });
};

/**
 * 获取菜单详情
 */
export const apiGetMenu = (params: any) => {
  return defHttp.get({ url: Api.MenuDetails, params });
};

/**
 * 新增菜单
 */
export const apiAddMenu = (data: any) => {
  return defHttp.post(
    { url: Api.Menu, data },
    {
      successMessageMode: 'message',
    },
  );
};
/**
 * 修改菜单
 */
export const apiUpdateMenu = (data: any) => {
  return defHttp.put(
    { url: Api.Menu, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 删除菜单
 */
export const apiDeleteMenu = (id: any) => {
  return defHttp.delete(
    { url: `${Api.Menu}/${id}` },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * 角色 id 查询授权菜单
 */
export const apiGetMenuByRoleId = (roleId: any) => {
  return defHttp.get({ url: `${Api.MenuTree}/${roleId}` });
};
