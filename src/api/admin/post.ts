import { defHttp } from '@/utils/http/axios';

enum Api {
  Post = '/admin/post',
  PostPage = '/admin/post/page',
  PostList = '/admin/post/list',
  PostDetails = '/admin/post/details',
}

/**
 * @description: 获取岗位分页列表
 */
export const apiGetPostPage = (params: any) => {
  return defHttp.get({ url: Api.PostPage, params });
};

/**
 * @description: 获取岗位列表
 */
export const apiGetPostList = (params?: any) => {
  return defHttp.get({ url: Api.PostList, params });
};

/**
 * @description: 获取岗位详情
 */
export const apiGetPost = (params: any) => {
  return defHttp.get({ url: Api.PostDetails, params });
};

/**
 * @description: id查询岗位信息
 */
export const apiGetPostById = (id: any) => {
  return defHttp.get({ url: `${Api.PostDetails}/${id}` });
};

/**
 * @description: 新增岗位
 */
export const apiAddPost = (data: any) => {
  return defHttp.post(
    { url: Api.Post, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 编辑岗位
 */
export const apiEditPost = (data: any) => {
  return defHttp.put(
    { url: Api.Post, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 删除岗位
 */
export const apiDelPost = (ids: any[]) => {
  return defHttp.delete(
    { url: Api.Post, data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 验证岗位名称是否存在
 */
export function validatePostName(_rule: any, value: any, isEdit: boolean): Promise<void> {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }

    apiGetPost({ postName: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('岗位名称已经存在'));
      } else {
        return resolve();
      }
    });
  });
}

/**
 * @description: 验证岗位编码是否存在
 */
export function validatePostCode(_rule: any, value: any, isEdit: boolean): Promise<void> {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }

    apiGetPost({ postCode: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('岗位编码已经存在'));
      } else {
        return resolve();
      }
    });
  });
}
