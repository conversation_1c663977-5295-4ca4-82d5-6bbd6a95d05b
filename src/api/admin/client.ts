import { defHttp } from '@/utils/http/axios';

enum Api {
  ClientPage = '/admin/client/page',
  Client = '/admin/client',
  ClientDetails = '/admin/client/getClientDetailsById',
  ClientSync = '/admin/client/sync',
}

/**
 * @description: 获取客户端分页列表
 */
export const apiGetClientPage = (params?: any) => {
  return defHttp.get({ url: Api.ClientPage, params });
};

/**
 * @description: 新增客户端
 */
export const apiAddClient = (data?: any) => {
  return defHttp.post(
    { url: Api.Client, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 获取客户端详情（通过ID）
 */
export const apiGetClientById = (id: string) => {
  return defHttp.get({ url: `${Api.Client}/${id}` });
};

/**
 * @description: 删除客户端
 */
export const apiDeleteClient = (data: any) => {
  return defHttp.delete(
    { url: Api.Client, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 修改客户端
 */
export const apiUpdateClient = (data?: any) => {
  return defHttp.put(
    { url: Api.Client, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 刷新客户端缓存
 */
export const apiRefreshClientCache = () => {
  return defHttp.put(
    { url: Api.ClientSync },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 获取客户端详情（通过客户端ID）
 */
export const apiGetClientDetails = (clientId: string) => {
  return defHttp.get({ url: `${Api.ClientDetails}/${clientId}` });
};

/**
 * @description: 验证客户端ID是否存在
 */
export const apiValidateClientId = async (rule: any, value: any, isEdit: boolean) => {
  if (isEdit) {
    return Promise.resolve();
  }
  apiGetClientDetails(value).then((res) => {
    const result = res;
    if (result !== null) {
      return Promise.reject(new Error('编号已经存在'));
    } else {
      return Promise.resolve();
    }
  });
};
