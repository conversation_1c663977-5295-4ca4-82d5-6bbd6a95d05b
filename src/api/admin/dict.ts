import { defHttp } from '@/utils/http/axios';

enum Api {
  DictType = '/admin/dict/type',
  DictList = '/admin/dict/list',
  DictItemPage = '/admin/dict/item/page',
  DictItem = '/admin/dict/item',
  Dict = '/admin/dict',
  DictDetails = '/admin/dict/details',
  DictItemDetails = '/admin/dict/item/details',
  DictSync = '/admin/dict/sync',
  DictExport = '/admin/dict/export',
}

/**
 * 根据字典类型获取字典数据
 * @param type 字典类型
 * @returns 字典数据
 */
export const apiGetDicts = (type: string) => defHttp.get({ url: `${Api.DictType}/${type}` });

/**
 * 获取字典列表（分页查询）
 * @param params 查询参数
 * @returns 字典列表数据
 */
export const apiFetchList = (params: any) => defHttp.get({ url: Api.DictList, params });

// 字典项相关接口
/**
 * 获取字典项分页列表
 * @param params 查询参数
 * @returns 字典项分页数据
 */
export const apiFetchItemList = (params: any) => defHttp.get({ url: Api.DictItemPage, params });

/**
 * 新增字典项
 * @param data 字典项数据
 * @returns 新增结果
 */
export const apiAddItemObj = (data: any) => defHttp.post({ url: Api.DictItem, data });

/**
 * 根据ID获取字典项详情
 * @param id 字典项ID
 * @returns 字典项详情
 */
export const apiGetItemObj = (id: string) => defHttp.get({ url: `${Api.DictItemDetails}/${id}` });

/**
 * 根据参数获取字典项详情
 * @param params 查询参数
 * @returns 字典项详情
 */
export const apiGetItemDetails = (params: any) => defHttp.get({ url: Api.DictItemDetails, params });

/**
 * 删除字典项
 * @param id 字典项ID
 * @returns 删除结果
 */
export const apiDelItemObj = (id: string) =>
  defHttp.delete(
    { url: `${Api.DictItem}/${id}` },
    {
      successMessageMode: 'message',
    },
  );

/**
 * 更新字典项
 * @param data 字典项数据
 * @returns 更新结果
 */
export const apiPutItemObj = (data: any) => defHttp.put({ url: Api.DictItem, data });

/**
 * 新增字典
 * @param data 字典数据
 * @returns 新增结果
 */
export const apiAddObj = (data: any) => defHttp.post({ url: Api.Dict, data });

/**
 * 根据ID获取字典详情
 * @param id 字典ID
 * @returns 字典详情
 */
export const apiGetObj = (id: string) => defHttp.get({ url: `${Api.DictDetails}/${id}` });

/**
 * 根据参数获取字典详情
 * @param params 查询参数
 * @returns 字典详情
 */
export const apiGetObjDetails = (params: any) => defHttp.get({ url: Api.DictDetails, params });

/**
 * 删除字典（支持批量删除）
 * @param ids 字典ID或ID数组
 * @returns 删除结果
 */
export const apiDelObj = (ids: any) =>
  defHttp.delete(
    { url: Api.Dict, data: ids },
    {
      successMessageMode: 'message',
    },
  );

/**
 * 更新字典
 * @param data 字典数据
 * @returns 更新结果
 */
export const apiPutObj = (data: any) => defHttp.put({ url: Api.Dict, data });

/**
 * 刷新字典缓存
 * @returns 刷新结果
 */
export const apiRefreshCache = () =>
  defHttp.put(
    { url: Api.DictSync },
    {
      successMessageMode: 'message',
    },
  );

/**
 * 导出字典数据
 * @param params 查询参数
 * @returns 导出文件流
 */
export const apiExportDict = (params: any) =>
  defHttp.get(
    { url: Api.DictExport, params, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );

// 验证函数
/**
 * 验证字典类型是否已存在
 * @param rule 验证规则（未使用但保持接口一致性）
 * @param value 字典类型值
 * @param callback 验证回调函数
 * @param isEdit 是否为编辑模式
 */
export function apiValidateDictType(_rule: any, value: any, callback: any, isEdit: boolean) {
  if (isEdit || !value) {
    callback();
    return;
  }

  apiGetObjDetails({ dictType: value })
    .then((response) => {
      const result = response.data;
      if (result !== null) {
        callback(new Error('字典已经存在'));
      } else {
        callback();
      }
    })
    .catch(() => {
      callback();
    });
}

/**
 * 验证字典项标签是否已存在
 * @param rule 验证规则（未使用但保持接口一致性）
 * @param value 字典项标签值
 * @param callback 验证回调函数
 * @param type 字典类型
 * @param isEdit 是否为编辑模式
 */
export function apiValidateDictItemLabel(
  _rule: any,
  value: any,
  callback: any,
  type: string,
  isEdit: boolean,
) {
  if (isEdit || !value || !type) {
    callback();
    return;
  }

  apiGetItemDetails({ dictType: type, label: value })
    .then((response) => {
      const result = response.data;
      if (result !== null) {
        callback(new Error('标签已经存在'));
      } else {
        callback();
      }
    })
    .catch(() => {
      callback();
    });
}
