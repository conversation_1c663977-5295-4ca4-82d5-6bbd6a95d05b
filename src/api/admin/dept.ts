import { defHttp } from '@/utils/http/axios';

enum Api {
  Dept = '/admin/dept',
  DeptTree = '/admin/dept/tree',
  DeptOrg = '/admin/dept/org',
  DeptOrgUserSearch = '/admin/dept/org/user/search',
}

/**
 *  获取部门列表
 */
export const apiGetDeptTree = (params?: any) => {
  return defHttp.get({ url: Api.DeptTree, params });
};

/**
 *  新增部门
 */
export const apiAddDept = (data?: any) => {
  return defHttp.post(
    { url: Api.Dept, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 *  修改部门
 */
export const apiUpdateDept = (data?: any) => {
  return defHttp.put(
    { url: Api.Dept, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 *  删除部门
 */
export const apiDeleteDept = (id?: any) => {
  return defHttp.delete(
    { url: `${Api.Dept}/${id}` },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 *  获取部门详情
 */
export const apiGetDept = (id?: any) => {
  return defHttp.get({ url: `${Api.Dept}/${id}` });
};
