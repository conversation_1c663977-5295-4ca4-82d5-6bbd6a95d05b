import { defHttp } from '@/utils/http/axios';
import { gettenantListResultModel, tenantModel } from './model/tenantModel';
import { getTenantMenuListResultModel, tenantMenuModel } from './model/tenantMenuModel';
import { ContentTypeEnum } from '@/enums/httpEnum';

enum Api {
  Tenant = '/admin/sys/tenant',
  TenantDetails = '/admin/sys/tenant/details',
  TenantPage = '/admin/sys/tenant/page',
  TenantMenu = '/admin/sys/tenant/menu',
  TenantMenuPage = '/admin/sys/tenant/menu/page',
  TenantMenuList = '/admin/sys/tenant/menu/list',
  TenantList = '/admin/tenant/list',
  TenantAddAgent = '/admin/sys/tenant/addAgentNum',
}

/**
 *
 * @param params
 * @description: 获取租户列表
 */
export function getTenantPage(params?: any): Promise<gettenantListResultModel> {
  return defHttp.get({ url: Api.TenantPage, params });
}

/**
 * @data data
 * @description: 新增租户
 */
export function addTenant(data: tenantModel) {
  return defHttp.post({ url: Api.Tenant, data }, { successMessageMode: 'message' });
}

/**
 * @data data
 * @description: 修改租户
 */
export function updateTenant(data: tenantModel) {
  return defHttp.put({ url: Api.Tenant, data }, { successMessageMode: 'message' });
}

/**
 * @id ids
 * @description: 删除租户
 */
export function deleteTenant(ids: string[]) {
  return defHttp.delete(
    {
      url: Api.Tenant,
      data: ids,
      headers: {
        'Content-Type': ContentTypeEnum.JSON,
      },
    },
    { successMessageMode: 'message' },
  );
}

/**
 * @params params
 * @description: 获取租户详情
 */
export function getTenantInfo(params: tenantModel) {
  return defHttp.get({ url: Api.TenantDetails, params });
}

/**
 *
 * @param params
 * @description: 获取租户套餐列表
 */
export function getTenantMenuPage(params?: any): Promise<getTenantMenuListResultModel> {
  return defHttp.get({ url: Api.TenantMenuPage, params });
}

/**
 * @data data
 * @description: 新增租户套餐
 */
export function addTenantMenu(data: tenantMenuModel) {
  return defHttp.post({ url: Api.TenantMenu, data }, { successMessageMode: 'message' });
}

/**
 * @data data
 * @description: 修改租户套餐
 */
export function updateTenantMenu(data: tenantMenuModel) {
  return defHttp.put({ url: Api.TenantMenu, data }, { successMessageMode: 'message' });
}

/**
 * @id id
 * @description: 删除租户套餐
 */
export function deleteTenantMenu(id: string) {
  return defHttp.delete({ url: Api.TenantMenu + '/' + id }, { successMessageMode: 'message' });
}

/**
 * @params params
 * @description: 获取租户套餐详情
 */
export function getTenantMenuInfo(params: tenantMenuModel) {
  return defHttp.get({ url: Api.TenantMenu, params });
}
/**
 * @params params
 * @description: 获取租户套餐列表
 */
export function getTenantMenuList() {
  return defHttp.get({ url: Api.TenantMenuList });
}

/**
 * @description 租户列表
 */
export function getTenantList() {
  return defHttp.get({ url: Api.TenantList });
}

/**
 * @description 新增租户下客服数量
 */
export function addTenantAgentNum(data: any) {
  return defHttp.post({ url: Api.TenantAddAgent, data }, { successMessageMode: 'message' });
}
