export interface UserItemModel {
  avatar: string; // 头像
  deptId: string; // 部门id
  deptName: string; // 部门名称
  email?: string; // 邮箱
  userId: string; // 用户id
  username: string; // 用户名
  nickname: string; // 昵称
  phone?: string; // 手机号
  password?: string; // 密码
  tenantId: string; // 租户id
  qqOpenid?: string; // qq openid
  wxOpenid?: string; // 微信openid
  postList: UserItemPostModel[]; // 岗位列表
  UserItemRoleModel: UserItemRoleModel[]; // 角色列表
}
export interface UserItemPostModel {
  postId: string; // 岗位id
  postName: string; // 岗位名称
  postCode: string; // 岗位编码
  postSort: string; // 岗位排序
  delFlag: string; // 删除标志
  createBy: string | null; // 创建者
  createTime: string; // 创建时间
  updateBy: string | null; // 更新者
  updateTime: string; // 更新时间
  remark: string; // 备注
}
export interface UserItemRoleModel {
  roleId: string; // 角色id
  roleName: string; // 角色名称
  roleCode: string; // 角色编码
  roleDesc: string; // 角色描述
  dsType: string; // 数据源
  dsScope: string; // 数据范围
  delFlag: string; // 删除标志
  createBy: string | null; // 创建者
  createTime: string; // 创建时间
  updateBy: string | null; // 更新者
  updateTime: string; // 更新时间
}
export type getUserListResultModel = UserItemModel[];
