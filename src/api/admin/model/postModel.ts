export interface postItemModel {
  postId: string; // 岗位ID
  postCode: string; // 岗位编码
  postName: string; // 岗位名称
  postSort: number; // 显示顺序
  remark: string; // 备注
  createBy: string; // 创建者
  createTime: string; // 创建时间
  updateBy: string; // 更新者
  updateTime: string; // 更新时间
  delFlag: string; // 删除标志
}
export interface postModel {
  postId?: string; // 岗位ID
  postCode?: string; // 岗位编码
  postName?: string; // 岗位名称
  postSort?: number; // 显示顺序
  remark?: string; // 备注
}

export type getpostListResultModel = postItemModel[];
