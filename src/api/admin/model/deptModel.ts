export interface deptItemModel {
  children?: deptItemModel[];
  createTime: string; // 创建时间
  id: string; // 主键
  name: string; // 部门名称
  parentId: string; // 父部门ID
  weight: number; // 权重
  isLock: boolean; // 是否锁定
}
export type getdeptListResultModel = deptItemModel[];

// 查询参数
export interface deptParams {
  deptName?: string; // 部门查询名称
  name?: string; // 部门名称
  parentId?: string; // 父部门ID
  sortOrder?: string; // 排序
  deptId?: string; // 部门ID
}
