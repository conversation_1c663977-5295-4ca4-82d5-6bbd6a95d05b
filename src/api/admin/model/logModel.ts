export interface RoutelogItem {
  createBy: string;
  createTime: string;
  delFlag: string;
  exception: string | null;
  id: string;
  logType: string;
  method: string;
  params: string;
  remoteAddr: string;
  requestUri: string;
  serviceId: string;
  time: string;
  title: string;
  updateTime: string | null;
  userAgent: string;
  tenantId?: number;
  body?: any;
}

/**
 * @description: 日志查询参数
 */
export interface LogQueryParams {
  current?: number;
  size?: number;
  logType?: string;
  title?: string;
  createBy?: string;
  remoteAddr?: string;
  requestUri?: string;
  method?: string;
  serviceId?: string;
  createTime?: string[];
  tenantId?: number;
}

/**
 * @description: Get log return value
 */
export type getlogListResultModel = RoutelogItem[];
