export interface tenantMenuItemModel {
  createBy: string; // 创建者
  createTime: string; // 创建时间
  id: string; // 主键
  name: string; // 租户套餐名称
  status: string; // 状态
  menuIds: string; // 套餐菜单
  updateBy: string; // 更新者
  updateTime: string; // 更新时间
}
// 租户列表
export type getTenantMenuListResultModel = tenantMenuItemModel[];

export interface tenantMenuModel {
  id?: string; // 主键
  name?: string; // 租户套餐名称
  status?: string; // 状态
  menuIds?: string; // 套餐菜单
}
