export interface RoleItemModel {
  createBy: string; // 创建者
  createTime: string; // 创建时间
  delFlag: string; // 删除标志（0代表存在 2代表删除）
  roleId: string; // 角色ID
  roleName: string; // 角色名称
  roleDesc: string; // 角色描述
  roleCode: string; // 角色标识
  dsType: string; // 数据权限类型
  dsScope: string; // 数据权限范围
  updateBy: string; // 更新者
  updateTime: string; // 更新时间
}

export type RoleListModel = RoleItemModel[];

export interface RoleDataModel {
  $dsType: string; // 数据权限类型
  dsScope?: string; // 数据权限范围
  dsType: string; // 数据权限类型
  roleCode: string; // 角色标识
  roleDesc: string; // 角色描述
  roleName: string; // 角色名称
  roleId?: string; // 角色ID
}
