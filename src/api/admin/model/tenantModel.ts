export interface tenantItemModel {
  code: string; // 租户编号
  createBy: string; // 创建者
  createTime: string; // 创建时间
  id: string; // 主键
  name: string; // 租户名称
  status: string; // 状态
  tenantDomain: string; // 租户域名
  startTime: string; // 开始时间
  endTime: string; // 结束时间
  updateBy: string; // 更新者
  updateTime: string; // 更新时间
}
// 租户列表
export type gettenantListResultModel = tenantItemModel[];

export interface tenantModel {
  id?: string; // 主键
  name?: string; // 租户名称
  code?: string; // 租户编号
  status?: string; // 状态
  tenantDomain?: string; // 租户域名
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
}
