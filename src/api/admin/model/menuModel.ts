export interface RouteItem {
  id: string;
  parentId: string;
  weight: number;
  name: string;
  path: string;
  componentPath: string | null;
  meta: DatumMeta;
  sortOrder: number;
  menuType: string;
  permission: string | null;
  children?: RouteItem[];
}
export interface DatumMeta {
  isLink: string;
  isIframe: boolean | null;
  isKeepAlive: boolean;
  icon: string;
  isAffix: boolean; // 待参数
  title: string;
  isHide: boolean;
}

/**
 * @description: Get menu return value
 */
export type getMenuListResultModel = RouteItem[];

export interface RoutelogItem {
  createBy: string;
  createTime: string;
  delFlag: string;
  exception: null;
  id: string;
  logType: string;
  method: string;
  params: string;
  remoteAddr: string;
  requestUri: string;
  serviceId: string;
  time: string;
  title: string;
  updateTime: null;
  userAgent: string;
}

/**
 * @description: Get log return value
 */
export type getlogListResultModel = RoutelogItem[];
