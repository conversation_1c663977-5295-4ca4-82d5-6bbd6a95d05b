import { defHttp } from '@/utils/http/axios';

enum Api {
  Role = '/admin/role',
  RolePage = '/admin/role/page',
  RoleList = '/admin/role/list',
  RoleDetails = '/admin/role/details',
  RoleMenu = '/admin/role/menu',
}
/**
 * @description: 获取角色列表
 */
export const apiGetRolePage = (params: any) => {
  return defHttp.get({ url: Api.RolePage, params });
};

/**
 * @description: 获取角色列表
 */
export const apiGetRoleList = (params?: any) => {
  return defHttp.get({ url: Api.RoleList, params });
};

/**
 * @description: 获取角色详情
 */
export const apiGetRole = (params: any) => {
  return defHttp.get({ url: Api.RoleDetails, params });
};

/**
 * @description: id查询角色信息
 */
export const apiGetRoleById = (id: any) => {
  return defHttp.get({ url: `${Api.RoleDetails}/${id}` });
};

/**
 * @description: 新增角色
 */
export const apiAddRole = (data: any) => {
  return defHttp.post(
    { url: Api.Role, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 编辑角色
 */

export const apiEditRole = (data: any) => {
  return defHttp.put(
    { url: Api.Role, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 删除角色
 */
export const apiDelRole = (ids: any[]) => {
  return defHttp.delete(
    { url: Api.Role, data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 角色授权菜单
 */
export const apiRoleMenu = (data: any) => {
  return defHttp.put(
    { url: Api.RoleMenu, data },
    {
      successMessageMode: 'message',
    },
  );
};

export function validateRoleCode(rule: any, value: any, isEdit: boolean): Promise<void> {
  console.log('validateRoleCode', value);
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }
    if (!value) {
      return resolve();
    }
    apiGetRole({ roleCode: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('角色标识已经存在'));
      } else {
        return resolve();
      }
    });
  });
}

export function validateRoleName(rule: any, value: any, isEdit: boolean): Promise<void> {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }

    if (!value) {
      return resolve();
    }

    apiGetRole({ roleName: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('角色名称已经存在'));
      } else {
        return resolve();
      }
    });
  });
}
