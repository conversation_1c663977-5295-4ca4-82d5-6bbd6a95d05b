import { defHttp } from '@/utils/http/axios';
import type { I18nRequest } from '#/onlineI18n';

enum Api {
  I18nPage = '/admin/i18n/page',
  I18nInfo = '/admin/i18n/info/{id}',
  I18n = '/admin/i18n',
  I18nItem = '/admin/i18n/updateItem',
  I18nByCodeList = '/admin/i18n/itemByCodeList',
}

// 获取国际化列表
export const apiGetI18nPage = (params: any) => {
  return defHttp.get({ url: Api.I18nPage, params });
};

// 获取国际化详情
export const apiGetI18nInfo = ({ id }: any) => {
  return defHttp.get({ url: Api.I18nInfo.replace('{id}', id) });
};

// 新增国际化
export const apiAddI18n = (data: any) => {
  return defHttp.post(
    { url: Api.I18n, data },
    {
      successMessageMode: 'message',
    },
  );
};

// 修改国际化
export const apiEditI18n = (data: any) => {
  return defHttp.put(
    { url: Api.I18n, data },
    {
      successMessageMode: 'message',
    },
  );
};

// 删除国际化
export const apiDelI18n = (ids: any) => {
  return defHttp.delete(
    { url: Api.I18n, data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

// 修改国际化项
export const apiEditI18nItem = (data: any) => {
  return defHttp.put(
    { url: Api.I18nItem, data },
    {
      successMessageMode: 'message',
    },
  );
};

// 根据code获取国际化项
export const apiGetI18nByCodeList = ({
  codes,
}: {
  codes: string[];
  locale: string;
}): Promise<I18nRequest[]> => {
  return defHttp.post({ url: Api.I18nByCodeList, data: codes });
};
