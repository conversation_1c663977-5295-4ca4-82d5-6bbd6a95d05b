import { defHttp } from '@/utils/http/axios';

enum Api {
  ParamPage = '/admin/param/page',
  Param = '/admin/param',
  ParamDetails = '/admin/param/details',
  ParamSync = '/admin/param/sync',
  ParamPublicValue = '/admin/param/publicValue',
}

/**
 * @description: 获取参数分页列表
 */
export const apiGetParamPage = (params?: any) => {
  return defHttp.get({ url: Api.ParamPage, params });
};

/**
 * @description: 新增参数
 */
export const apiAddParam = (data?: any) => {
  return defHttp.post(
    { url: Api.Param, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 获取参数详情（通过ID）
 */
export const apiGetParamById = (id: string) => {
  return defHttp.get({ url: `${Api.ParamDetails}/${id}` });
};

/**
 * @description: 删除参数
 */
export const apiDeleteParam = (ids: string[]) => {
  return defHttp.delete(
    { url: Api.Param, data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 修改参数
 */
export const apiUpdateParam = (data?: any) => {
  return defHttp.put(
    { url: Api.Param, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 刷新参数缓存
 */
export const apiRefreshParamCache = () => {
  return defHttp.put(
    { url: Api.ParamSync },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 获取参数详情（通过查询条件）
 */
export const apiGetParamDetails = (params?: any) => {
  return defHttp.get({ url: Api.ParamDetails, params });
};

/**
 * @description: 根据key获取参数值
 */
export const apiGetParamValue = (key: string) => {
  return defHttp.get({ url: `${Api.ParamPublicValue}/${key}` });
};

/**
 * @description: 验证参数编码是否存在
 */
export const apiValidateParamCode = async (_rule: any, value: any, isEdit: boolean) => {
  if (isEdit || !value) {
    return Promise.resolve();
  }
  try {
    const result = await apiGetParamDetails({ publicKey: value });
    if (result !== null) {
      return Promise.reject('参数名称已经存在');
    } else {
      return Promise.resolve();
    }
  } catch (error) {
    return Promise.resolve();
  }
};

/**
 * @description: 验证参数名称是否存在
 */
export const apiValidateParamName = async (_rule: any, value: any, isEdit: boolean) => {
  if (isEdit || !value) {
    return Promise.resolve();
  }

  try {
    const result = await apiGetParamDetails({ publicName: value });
    if (result !== null) {
      return Promise.reject('参数名称已经存在');
    } else {
      return Promise.resolve();
    }
  } catch (error) {
    return Promise.resolve();
  }
};
