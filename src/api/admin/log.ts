import { defHttp } from '@/utils/http/axios';
import { LogQueryParams } from './model/logModel';

enum Api {
  Log = '/admin/log',
  LogPage = '/admin/log/page',
  LogDetails = '/admin/log/details',
  LogExport = '/admin/log/export',
}

/**
 * @description: 获取日志分页列表
 */
export const apiGetLogPage = (params: LogQueryParams) => {
  return defHttp.get({ url: Api.LogPage, params });
};

/**
 * @description: 删除日志
 */
export const apiDeleteLog = (ids: string[]) => {
  return defHttp.delete(
    { url: Api.Log, data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 导出日志
 */
export const apiExportLog = (params: LogQueryParams) => {
  return defHttp.get(
    { url: Api.LogExport, params, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description: 清空日志
 */
export const apiClearLog = () => {
  return defHttp.delete(
    { url: Api.Log },
    {
      successMessageMode: 'message',
    },
  );
};
