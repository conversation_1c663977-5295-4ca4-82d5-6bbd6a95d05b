import { type Ref, ref, unref, watch } from 'vue';

interface UseApiLoadingReturn<T = any> {
  loading: Ref<boolean>;
  reload: (_params?: any) => Promise<T>;
  apiResult: Ref<T | undefined>;
}

export function useApiLoading<T = any>({
  api,
  params,
  immediate = true,
  watchParams = true,
}: {
  api: (...arg: any[]) => Promise<T>;
  params?: Ref<any> | Recordable;
  immediate?: boolean;
  watchParams?: boolean;
}): UseApiLoadingReturn<T> {
  const loading = ref(false);
  const apiResult = ref<T | undefined>();
  const reload = async (_params?: any) => {
    try {
      loading.value = true;
      const queryParams = _params || params;
      apiResult.value = await (queryParams ? api(unref(queryParams)) : api());
      return Promise.resolve(apiResult.value);
    } catch (e) {
      return Promise.reject(e);
    } finally {
      loading.value = false;
    }
  };

  if (watchParams && params) {
    watch(
      () => params,
      () => {
        reload();
      },
    );
  }
  immediate && reload();

  return {
    loading,
    reload,
    apiResult,
  };
}
