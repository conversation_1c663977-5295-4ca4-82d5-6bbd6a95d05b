import { type Ref, ref } from 'vue';
import { downloadByData } from '@/utils/file/download';
import { message } from 'ant-design-vue';
// 定义传参接口
export interface ApiExportParams {
  api: (params?: any) => Promise<any>;
  fileName?: string;
}

export interface ApiExportResult {
  reload: (_params?: any) => Promise<void>;
  loading: Ref<boolean>;
}

function extractFileNameFromHeader(header: string, defaultName: string): string {
  if (!header) return defaultName;

  // 优先匹配 filename*=utf-8''encodedFileName
  const filenameStarMatch = header.match(/filename\*\s*=\s*([^;]+)/i);
  if (filenameStarMatch) {
    const encodedFileName = filenameStarMatch[1].trim().replace(/^utf-8''/i, '');
    try {
      return decodeURIComponent(encodedFileName);
    } catch {
      return encodedFileName; // fallback: 如果不是合法 URI 编码
    }
  }

  // fallback: 匹配 filename="xxx"
  const filenameMatch = header.match(/filename\s*=\s*["']?([^;"']+)["']?/i);
  if (filenameMatch) {
    return filenameMatch[1].trim();
  }

  return defaultName;
}

export function useApiExport({ api, fileName = 'download' }: ApiExportParams): ApiExportResult {
  const loading = ref(false);
  const reload = async (_params?: any) => {
    try {
      loading.value = true;
      const response = await api(_params);
      // 处理不同类型的响应
      if (response instanceof Blob) {
        downloadByData(response, fileName);
      } else if (response.data instanceof Blob) {
        const fullFileName = extractFileNameFromHeader(
          response.headers['content-disposition'],
          fileName,
        );
        downloadByData(response.data, fullFileName);
      } else if (response.url) {
        // 如果返回的是下载链接
        window.open(response.url, '_blank');
      } else {
        message.error('下载失败：响应格式不正确');
        return;
      }
    } catch (error) {
      return Promise.reject(error);
    } finally {
      loading.value = false;
    }
  };
  return {
    reload,
    loading,
  };
}
