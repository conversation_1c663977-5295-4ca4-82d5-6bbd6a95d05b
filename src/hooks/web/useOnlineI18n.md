# useOnlineI18n Hook 使用文档

基于现有 `OnlineI18nCache` 类实现的 Vue 3 组合式 API，提供响应式的在线翻译功能。

## 功能特性

- 🚀 **响应式翻译**: 基于 Vue 3 响应式系统，翻译内容自动更新
- 📦 **智能缓存**: 基于现有缓存系统，避免重复请求
- 🔄 **语言切换**: 自动响应语言变化，重新获取翻译
- ⚡ **批量加载**: 支持批量预加载翻译，提升性能
- 🛠 **TypeScript**: 完整的 TypeScript 类型支持
- 🎯 **灵活使用**: 提供多种使用方式，适应不同场景

## API 概览

### 1. useOnlineI18n - 单个翻译

用于获取单个翻译文本的响应式 Hook。

```typescript
const { t, loading, error, refresh } = useOnlineI18n(code, defaultValue);
```

**参数:**
- `code: string` - 翻译代码
- `defaultValue: string` - 默认值（可选，默认为空字符串）

**返回值:**
- `t: ComputedRef<string>` - 翻译文本
- `loading: ComputedRef<boolean>` - 加载状态
- `error: ComputedRef<string | null>` - 错误信息
- `refresh: () => void` - 手动刷新翻译

### 2. useOnlineI18nBatch - 批量翻译

用于批量获取多个翻译文本的响应式 Hook。

```typescript
const { translations, loading, error, refresh, t } = useOnlineI18nBatch(codes);
```

**参数:**
- `codes: string[] | { code: string; defaultValue?: string }[]` - 翻译代码数组

**返回值:**
- `translations: ComputedRef<Record<string, string>>` - 翻译映射对象
- `loading: ComputedRef<boolean>` - 加载状态
- `error: ComputedRef<string | null>` - 错误信息
- `refresh: () => void` - 手动刷新翻译
- `t: (code: string, fallback?: string) => ComputedRef<string>` - 获取单个翻译的便捷方法

### 3. useOnlineI18nFunction - 翻译函数

提供类似 vue-i18n 的 t 函数功能。

```typescript
const { t, tSync } = useOnlineI18nFunction();
```

**返回值:**
- `t: (code: string, defaultValue?: string) => Promise<string>` - 异步翻译函数
- `tSync: (code: string, defaultValue?: string) => string` - 同步翻译函数

### 4. useOnlineI18nPreload - 预加载翻译

用于预加载页面或组件需要的翻译。

```typescript
const { preload } = useOnlineI18nPreload(codes);
```

## 使用示例

### 基础使用

```vue
<template>
  <div>
    <h1 v-if="!titleLoading">{{ title }}</h1>
    <div v-else>加载中...</div>
    <button @click="refreshTitle">刷新</button>
  </div>
</template>

<script setup lang="ts">
import { useOnlineI18n } from '@/hooks/web/useOnlineI18n';

const { t: title, loading: titleLoading, refresh: refreshTitle } = useOnlineI18n(
  'page.title',
  '默认标题'
);
</script>
```

### 批量翻译

```vue
<template>
  <div v-if="!loading">
    <button>{{ saveText }}</button>
    <button>{{ cancelText }}</button>
    <button>{{ deleteText }}</button>
  </div>
</template>

<script setup lang="ts">
import { useOnlineI18nBatch } from '@/hooks/web/useOnlineI18n';

const codes = ['button.save', 'button.cancel', 'button.delete'];
const { translations, loading, t } = useOnlineI18nBatch(codes);

const saveText = t('button.save', '保存');
const cancelText = t('button.cancel', '取消');
const deleteText = t('button.delete', '删除');
</script>
```

### 表单翻译

```vue
<template>
  <form v-if="!formLoading">
    <div>
      <label>{{ nameLabel }}</label>
      <input v-model="form.name" />
    </div>
    <div>
      <label>{{ emailLabel }}</label>
      <input v-model="form.email" />
    </div>
    <button @click="handleSave">{{ saveText }}</button>
  </form>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useOnlineI18nBatch, useOnlineI18nFunction } from '@/hooks/web/useOnlineI18n';

// 表单字段翻译
const formCodes = [
  { code: 'form.name', defaultValue: '姓名' },
  { code: 'form.email', defaultValue: '邮箱' },
];

const { loading: formLoading, t: getFormLabel } = useOnlineI18nBatch(formCodes);
const nameLabel = getFormLabel('form.name');
const emailLabel = getFormLabel('form.email');

// 按钮翻译
const { t: saveText } = useOnlineI18n('button.save', '保存');

// 消息翻译
const { t: getMessage } = useOnlineI18nFunction();

const form = ref({ name: '', email: '' });

const handleSave = async () => {
  try {
    // 保存逻辑
    const message = await getMessage('message.success', '保存成功');
    alert(message);
  } catch (error) {
    const message = await getMessage('message.error', '保存失败');
    alert(message);
  }
};
</script>
```

### 预加载翻译

```vue
<script setup lang="ts">
import { onMounted } from 'vue';
import { useOnlineI18nPreload } from '@/hooks/web/useOnlineI18n';

// 预加载页面所需的所有翻译
const pageCodes = [
  'page.title',
  'page.description',
  'button.save',
  'button.cancel',
  'form.name',
  'form.email',
];

const { preload } = useOnlineI18nPreload(pageCodes);

onMounted(async () => {
  // 页面加载时预加载翻译
  await preload();
  console.log('翻译预加载完成');
});
</script>
```

## 最佳实践

### 1. 合理使用缓存

Hook 基于现有的 `OnlineI18nCache` 类，会自动缓存翻译结果。相同的翻译代码在同一语言下只会请求一次。

### 2. 批量加载优化

对于需要多个翻译的组件，推荐使用 `useOnlineI18nBatch` 而不是多个 `useOnlineI18n`，这样可以减少网络请求。

### 3. 预加载策略

对于重要页面，可以在路由进入前或组件挂载时预加载翻译，提升用户体验。

### 4. 错误处理

始终处理加载错误，提供合适的默认值和用户反馈。

### 5. 响应式使用

Hook 返回的都是响应式引用，可以直接在模板中使用，会自动响应语言切换。

## 注意事项

1. **异步特性**: 翻译加载是异步的，需要处理加载状态
2. **语言切换**: Hook 会自动响应语言变化，重新获取翻译
3. **缓存机制**: 基于现有缓存系统，避免重复请求
4. **错误处理**: 网络错误时会返回默认值，并提供错误信息
5. **性能优化**: 使用防抖机制避免频繁请求

## 与现有系统集成

这个 Hook 完全基于现有的 `OnlineI18nCache` 类和 API 接口，无需修改后端接口，可以直接在项目中使用。
