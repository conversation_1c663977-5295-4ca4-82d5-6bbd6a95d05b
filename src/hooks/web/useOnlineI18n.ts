import { computed, ref, watch, unref, type Ref } from 'vue';
import { OnlineI18nCache } from '@/onlineI18n/cache';
import { useLocaleStoreWithOut } from '@/store/modules/locale';
import { tryOnScopeDispose } from '@vueuse/core';
import { CreateMapParams, CreateMapReturnI18n } from '#/utils';
import { isArray, map } from 'lodash-es';

const cache = OnlineI18nCache.getInstance();

type TranslationReturn = {
  t: Ref<string>;
  loading: Ref<boolean>;
  error: Ref<string | null>;
  refresh: () => void;
};

type TFunction = {
  (code: string, defaultValue?: string): Ref<string>;
  (code: [string, string?]): Ref<string>;
};

type TranslationFnReturn = {
  t: TFunction;
};

/**
 * 多功能 Hook：
 * - useOnlineI18n('code', '默认值')：响应式单词条
 * - useOnlineI18n()：返回全局翻译函数 t(code, default)
 */
export function useOnlineI18n(): TranslationFnReturn;
export function useOnlineI18n(code: string, defaultValue?: string): TranslationReturn;
export function useOnlineI18n(code: [string, string?]): TranslationReturn;
export function useOnlineI18n(code?: string | [string, string?], defaultValue?: string): any {
  const localeStore = useLocaleStoreWithOut();

  // 无参数模式，返回 { t }
  if (code === undefined) {
    const usedCodes = new Set<string>();

    const t = (input: string | [string, string?], defaultValue?: string) => {
      let code: string;
      let fallback: string;

      if (Array.isArray(input)) {
        code = input[0];
        fallback = input[1] ?? input[0];
      } else {
        code = input;
        fallback = defaultValue ?? code;
      }

      const cacheItem = cache.getTranslation(code, fallback);

      if (!usedCodes.has(code)) {
        const stop = watch(
          () => localeStore.getLocale,
          () => {
            cache.getTranslation(code, fallback);
          },
        );
        tryOnScopeDispose(stop);
        usedCodes.add(code);
      }

      return computed(() => cacheItem.value.value || fallback);
    };

    return { t };
  }

  // 数组形式处理：[code, defaultValue?]
  let finalCode: string;
  let finalDefault: string;

  if (Array.isArray(code)) {
    finalCode = code[0];
    finalDefault = code[1] ?? finalCode;
  } else {
    finalCode = code;
    finalDefault = defaultValue ?? finalCode;
  }

  const error = ref<string | null>(null);
  const cacheItem = cache.getTranslation(finalCode, finalDefault);

  const load = async () => {
    try {
      error.value = null;
      cache.getTranslation(finalCode, finalDefault);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '翻译加载失败';
    }
  };

  const stop = watch(() => localeStore.getLocale, load, { immediate: true });
  tryOnScopeDispose(stop);

  return {
    t: computed(() => cacheItem.value.value || finalDefault),
    loading: computed(() => cacheItem.loading.value),
    error,
    refresh: load,
  };
}
export function useOnlineI18nBatch(
  codes:
    | Ref<string[]>
    | string[]
    | Ref<{ code: string; defaultValue?: string }[]>
    | { code: string; defaultValue?: string }[],
) {
  const localeStore = useLocaleStoreWithOut();
  const error = ref<string | null>(null);

  const normalizedCodes = computed(() => {
    const list = unref(codes);
    return list.map((item) =>
      typeof item === 'string'
        ? { code: item, defaultValue: '' }
        : { code: item.code, defaultValue: item.defaultValue || '' },
    );
  });

  const translationMap: Record<string, Ref<string>> = {};
  const loadingMap: Record<string, Ref<boolean>> = {};

  const preload = () => {
    for (const { code, defaultValue } of normalizedCodes.value) {
      const item = cache.getTranslation(code, defaultValue);
      translationMap[code] = item.value;
      loadingMap[code] = item.loading;
    }
  };

  const stop = watch([() => localeStore.getLocale, normalizedCodes], preload, {
    immediate: true,
    deep: true,
  });
  tryOnScopeDispose(stop);

  return {
    translations: computed(() => {
      const result: Record<string, string> = {};
      for (const key in translationMap) {
        result[key] = translationMap[key].value;
      }
      return result;
    }),
    loading: computed(() => {
      return Object.values(loadingMap).some((l) => l.value);
    }),
    error: computed(() => error.value),
    refresh: preload,
    t: (code: string, fallback?: string) => {
      return computed(() => {
        return translationMap[code]?.value || fallback || code;
      });
    },
  };
}

export function useOnlineI18nFunction() {
  const t = async (code: string, defaultValue: string = code): Promise<string> => {
    try {
      const item = cache.getTranslation(code, defaultValue);
      if (!item.loading.value) return item.value.value;

      return new Promise((resolve) => {
        const unwatch = watch(item.loading, (loading) => {
          if (!loading) {
            resolve(item.value.value);
            unwatch();
          }
        });
      });
    } catch (e) {
      console.error('Translation failed:', e);
      return defaultValue;
    }
  };

  const tSync = (code: string, defaultValue: string = code): string => {
    try {
      return cache.getTranslation(code, defaultValue).value.value;
    } catch (e) {
      console.error('Sync translation failed:', e);
      return defaultValue;
    }
  };

  return { t, tSync };
}

export function useOnlineI18nPreload(
  codes:
    | Ref<string[]>
    | string[]
    | Ref<{ code: string; defaultValue?: string }[]>
    | { code: string; defaultValue?: string }[],
) {
  const localeStore = useLocaleStoreWithOut();

  const normalizedCodes = computed(() => {
    const list = unref(codes);
    return list.map((item) =>
      typeof item === 'string'
        ? { code: item, defaultValue: '' }
        : { code: item.code, defaultValue: item.defaultValue || '' },
    );
  });

  const preload = async () => {
    await cache.getTranslations(normalizedCodes.value);
  };

  const stop = watch([() => localeStore.getLocale, normalizedCodes], preload, {
    immediate: true,
    deep: true,
  });
  tryOnScopeDispose(stop);

  return { preload };
}

export function useMapWithI18n(list: CreateMapParams[]): CreateMapReturnI18n {
  const { t } = useOnlineI18n();
  return map(list, ([value, label, color, extend]) => {
    if (typeof label === 'string') {
      return { value, label, color, extend };
    } else if (isArray(label)) {
      return { value, label: t(label), color, extend };
    }
    return { value, label, color, extend };
  });
}
