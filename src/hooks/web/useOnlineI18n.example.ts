/**
 * 在线 i18n Hook 使用示例
 *
 * 本文件展示了如何在 Vue 组件中使用在线 i18n Hook
 */

import { ref, computed } from 'vue';
import {
  useOnlineI18n,
  useOnlineI18nBatch,
  useOnlineI18nFunction,
  useOnlineI18nPreload,
} from './useOnlineI18n';

// ============= 示例 1: 单个翻译使用 =============
export function singleTranslationExample() {
  // 基本用法
  const {
    t: title,
    loading: titleLoading,
    error: titleError,
    refresh: refreshTitle,
  } = useOnlineI18n('page.title', '默认标题');

  // 在模板中使用
  // <h1 v-if="!titleLoading">{{ title }}</h1>
  // <div v-else>加载中...</div>
  // <div v-if="titleError" class="error">{{ titleError }}</div>
  // <button @click="refreshTitle">刷新标题</button>

  return {
    title,
    titleLoading,
    titleError,
    refreshTitle,
  };
}

// ============= 示例 2: 批量翻译使用 =============
export function batchTranslationExample() {
  // 使用字符串数组
  const codes = ref(['button.save', 'button.cancel', 'button.delete']);

  const {
    translations,
    loading: batchLoading,
    error: batchError,
    refresh: refreshBatch,
    t: getTranslation,
  } = useOnlineI18nBatch(codes);

  // 获取单个翻译
  const saveButtonText = getTranslation('button.save', '保存');
  const cancelButtonText = getTranslation('button.cancel', '取消');

  // 在模板中使用
  // <div v-if="!batchLoading">
  //   <button>{{ saveButtonText }}</button>
  //   <button>{{ cancelButtonText }}</button>
  //   <button>{{ translations['button.delete'] || '删除' }}</button>
  // </div>

  return {
    translations,
    batchLoading,
    batchError,
    refreshBatch,
    saveButtonText,
    cancelButtonText,
  };
}

// ============= 示例 3: 带默认值的批量翻译 =============
export function batchWithDefaultsExample() {
  const codesWithDefaults = ref([
    { code: 'form.name', defaultValue: '姓名' },
    { code: 'form.email', defaultValue: '邮箱' },
    { code: 'form.phone', defaultValue: '电话' },
  ]);

  const { translations, loading, t } = useOnlineI18nBatch(codesWithDefaults);

  // 获取表单标签
  const nameLabel = t('form.name');
  const emailLabel = t('form.email');
  const phoneLabel = t('form.phone');

  return {
    translations,
    loading,
    nameLabel,
    emailLabel,
    phoneLabel,
  };
}

// ============= 示例 4: 翻译函数使用 =============
export function translationFunctionExample() {
  const { t, tSync } = useOnlineI18nFunction();

  // 异步获取翻译
  const getAsyncTranslation = async () => {
    const message = await t('message.success', '操作成功');
    console.log('异步翻译结果:', message);
    return message;
  };

  // 同步获取翻译（返回缓存值或默认值）
  const getSyncTranslation = () => {
    const message = tSync('message.error', '操作失败');
    console.log('同步翻译结果:', message);
    return message;
  };

  return {
    getAsyncTranslation,
    getSyncTranslation,
  };
}

// ============= 示例 5: 预加载翻译 =============
export function preloadExample() {
  // 预加载页面需要的所有翻译
  const pageCodes = ref([
    'page.title',
    'page.description',
    'button.save',
    'button.cancel',
    'form.name',
    'form.email',
  ]);

  const { preload } = useOnlineI18nPreload(pageCodes);

  // 手动触发预加载
  const handlePreload = async () => {
    await preload();
    console.log('预加载完成');
  };

  return {
    handlePreload,
  };
}

// ============= 示例 6: 在 Vue 组件中的完整使用 =============
export function completeComponentExample() {
  // 页面标题
  const { t: pageTitle, loading: titleLoading } = useOnlineI18n('user.profile.title', '用户资料');

  // 表单字段翻译
  const formCodes = ref([
    { code: 'user.form.name', defaultValue: '姓名' },
    { code: 'user.form.email', defaultValue: '邮箱' },
    { code: 'user.form.phone', defaultValue: '电话' },
    { code: 'user.form.address', defaultValue: '地址' },
  ]);

  const {
    translations: formTranslations,
    loading: formLoading,
    t: getFormLabel,
  } = useOnlineI18nBatch(formCodes);

  // 按钮翻译
  const buttonCodes = ['button.save', 'button.cancel', 'button.reset'];
  const { translations: buttonTranslations, t: getButtonText } = useOnlineI18nBatch(buttonCodes);

  // 消息翻译函数
  const { t: getMessage } = useOnlineI18nFunction();

  // 表单数据
  const formData = ref({
    name: '',
    email: '',
    phone: '',
    address: '',
  });

  // 保存操作
  const handleSave = async () => {
    try {
      // 模拟保存操作
      console.log('保存数据:', formData.value);

      // 显示成功消息
      const successMessage = await getMessage('message.save.success', '保存成功');
      alert(successMessage);
    } catch (error) {
      // 显示错误消息
      const errorMessage = await getMessage('message.save.error', '保存失败');
      alert(errorMessage);
    }
  };

  // 计算属性
  const isLoading = computed(() => titleLoading.value || formLoading.value);

  return {
    // 翻译相关
    pageTitle,
    formTranslations,
    buttonTranslations,
    getFormLabel,
    getButtonText,
    getMessage,
    isLoading,

    // 表单相关
    formData,
    handleSave,
  };
}

// ============= 示例 7: 动态代码列表 =============
export function dynamicCodesExample() {
  const selectedModule = ref('user');

  // 根据选中的模块动态生成翻译代码
  const dynamicCodes = computed(() => {
    const module = selectedModule.value;
    return [
      `${module}.title`,
      `${module}.description`,
      `${module}.form.name`,
      `${module}.form.email`,
    ];
  });

  const { translations, loading } = useOnlineI18nBatch(dynamicCodes);

  // 切换模块
  const switchModule = (module: string) => {
    selectedModule.value = module;
  };

  return {
    selectedModule,
    translations,
    loading,
    switchModule,
  };
}

// ============= 示例 8: 错误处理 =============
export function errorHandlingExample() {
  const { t, loading, error, refresh } = useOnlineI18n('invalid.code', '默认文本');

  // 处理错误
  const handleError = () => {
    if (error.value) {
      console.error('翻译加载失败:', error.value);
      // 可以显示错误提示或重试
      refresh();
    }
  };

  return {
    t,
    loading,
    error,
    handleError,
    refresh,
  };
}
