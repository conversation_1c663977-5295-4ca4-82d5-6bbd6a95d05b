import type { ComputedRef } from 'vue';
import { computed, unref, ref } from 'vue';

interface StepConfig {
  nextFn?: (any) => void;
  prevFn?: (any) => void;
  redoFn?: (any) => void;
  // step 数量
  stepNum?: number;
  // 默认显示第几步
  defaultStep?: number;
}

interface ShowStep {
  current: number;
  [key: string]: boolean | number;
}

type returnType = [
  ComputedRef<ShowStep>,
  {
    prevStep: (v?: any) => void;
    nextStep: (v?: any) => void;
    redoStep: () => void;
    setStep: (v: number) => void;
  },
];

export function useFormStep(stepConfig?: StepConfig): returnType {
  const stepNum = stepConfig?.stepNum || 5;
  const defaultStep = stepConfig?.defaultStep || 1;
  const current = ref(defaultStep);
  const lastCurrent = ref(defaultStep);
  const showStep = computed<ShowStep>(() => {
    const cur = unref(current);
    const last = unref(lastCurrent);
    const o = {
      current: cur - 1 || 0,
    };
    for (let index = 1; index <= stepNum; index++) {
      const currKey = `step${index}`;
      const lastkey = `init${index}`;
      o[currKey] = cur === index;
      o[lastkey] = last >= index;
    }

    return o;
  });
  // 上一步
  function prevStep() {
    current.value--;
  }
  // 下一步
  function nextStep(v) {
    current.value++;
    if (lastCurrent.value <= current.value) {
      lastCurrent.value = current.value;
    }
    stepConfig?.nextFn && stepConfig.nextFn(v);
  }

  // 指定跳转
  function setStep(index: number) {
    // 判断 index 是否合法
    if (index < 0 || index > stepNum) {
      return;
    }
    current.value = index;
    if (lastCurrent.value <= current.value) {
      lastCurrent.value = current.value;
    }
  }

  // 重置
  function redoStep() {
    current.value = 1;
    lastCurrent.value = 1;
  }
  return [showStep, { prevStep, nextStep, redoStep, setStep }];
}
