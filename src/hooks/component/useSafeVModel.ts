import { ref, computed, watch, getCurrentInstance, nextTick, queuePostFlushCb } from 'vue';
import { isEqual } from 'lodash-es';

export interface UseSafeVModelOptions<T> {
  prop?: string;
  event?: string | string[];
  defaultValue?: T;
  deep?: boolean;
  dispatchTiming?: 'sync' | 'nextTick' | 'postFlush';
  payload?: any[] | ((value: T) => any[]);
  isEqual?: (a: T, b: T) => boolean;
}

/**
 * 用于替代 v-model，支持受控 / 非受控 + 安全同步
 */
export function useSafeVModel<T = any>(
  props: Record<string, any>,
  prop: string = 'modelValue',
  options: UseSafeVModelOptions<T> = {},
) {
  const instance = getCurrentInstance();
  const emit = instance?.emit;

  const {
    event = `update:${prop}`,
    defaultValue,
    deep = false,
    dispatchTiming = 'sync',
    payload,
    isEqual: equal = isEqual,
  } = options;

  // ✅ 立即取 prop 值，避免初始不一致
  const local = ref<T>(props[prop] !== undefined ? props[prop] : (defaultValue as T));

  const emitValue = (val: T) => {
    const events = Array.isArray(event) ? event : [event];
    const extra = typeof payload === 'function' ? payload(val) : (payload ?? []);
    events.forEach((e) => emit?.(e, val, ...extra));
  };

  const scheduleEmit = (val: T) => {
    switch (dispatchTiming) {
      case 'nextTick':
        nextTick(() => emitValue(val));
        break;
      case 'postFlush':
        queuePostFlushCb(() => emitValue(val));
        break;
      default:
        emitValue(val);
    }
  };

  const state = computed<T>({
    get() {
      return local.value;
    },
    set(val: T) {
      if (equal(val, local.value)) return;
      local.value = val;
      scheduleEmit(val);
    },
  });

  watch(
    () => props[prop],
    (val) => {
      if (!equal(val, local.value)) {
        local.value = val;
      }
    },
    { deep },
  );

  return state;
}
