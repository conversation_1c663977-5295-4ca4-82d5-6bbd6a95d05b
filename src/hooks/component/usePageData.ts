import { ref } from 'vue';
import { PaginationData, PaginationConfig, RefreshParams } from '@/utils/pagination';

export function usePageData(props: PaginationConfig) {
  const { immediate = true } = props;
  const list = ref<any[]>([]);
  const loading = ref(false);

  const pagination = ref<any>({});
  // 是否已经初始化过了
  const listInit = ref(false);
  // 分页实列
  const dataInstance = new PaginationData<any>(Object.assign({}, props, { model: false }));
  // 初始化后开始获取数据

  const getPage = () => {
    pagination.value = {
      ...dataInstance.getAntdPaginationData(),
      onChange: goPage,
      onShowSizeChange,
    };
  };

  const onShowSizeChange = (current, pageSize) => {
    dataInstance.setPageSize(pageSize);
    dataInstance.refresh();
  };

  const reload = async (params?: RefreshParams) => {
    try {
      loading.value = true;
      listInit.value = true;
      list.value = await dataInstance.refresh(params);
      getPage();
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const next = async () => {
    try {
      loading.value = true;
      list.value = await dataInstance.nextPage();
      getPage();
    } finally {
      loading.value = false;
    }
  };

  // 页码跳转
  const goPage = async (page: number) => {
    try {
      loading.value = true;
      list.value = await dataInstance.goPage({ current: page });
      getPage();
    } finally {
      loading.value = false;
    }
  };

  if (immediate) {
    reload();
  }

  return {
    list,
    loading,
    pagination,
    next,
    goPage,
    reload,
    listInit,
  };
}
