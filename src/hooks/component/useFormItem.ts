import type { UnwrapRef, Ref, WritableComputedRef, DeepReadonly } from 'vue';
import { reactive, readonly, computed, getCurrentInstance, watchEffect, unref, toRaw } from 'vue';

import { isEqual } from 'lodash-es';

export function useRuleFormItem<T extends Recordable, K extends keyof T, V = UnwrapRef<T[K]>>(
  props: T,
  key?: K,
  changeEvent?,
  emitData?: Ref<any[]>,
): [WritableComputedRef<V>, (val: V) => void, DeepReadonly<V>];

export function useRuleFormItem<T extends Recordable>(
  props: T,
  key: keyof T = 'value',
  changeEvent = 'change',
  emitData?: Ref<any[]>,
) {
  const instance = getCurrentInstance();
  const emit = instance?.emit;
  const changeEvents: string[] = [];
  if (changeEvent) {
    if (Array.isArray(changeEvent)) {
      changeEvents.push(...changeEvent); // 确保 changeEvent 是一个字符串数组
    } else if (typeof changeEvent === 'string') {
      changeEvents.push(changeEvent); // 确保 changeEvent 是一个字符串
    }
  }
  const innerState = reactive({
    value: props[key],
  });

  const defaultState = readonly(innerState);

  const setState = (val: UnwrapRef<T[keyof T]>): void => {
    innerState.value = val as T[keyof T];
  };

  watchEffect(() => {
    innerState.value = props[key];
  });

  const state: any = computed({
    get() {
      return innerState.value;
    },
    set(value) {
      if (isEqual(value, defaultState.value)) return;

      innerState.value = value as T[keyof T];

      // nexttick无法确保数据已经更新
      // 所以使用setTimeout0ms确保数据已经更新
      // 极端情况下可能还会有问题，但相对于nextTick概率小很多
      setTimeout(() => {
        changeEvents.forEach((eventName) => {
          emit?.(eventName, value, ...(toRaw(unref(emitData)) || []));
        });
      }, 0);
    },
  });

  return [state, setState, defaultState];
}
