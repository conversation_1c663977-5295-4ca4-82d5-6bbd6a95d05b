import { reactive } from 'vue';

export function useLoadingButton(keys: string[] = []) {
  const initState = keys.reduce((acc, key) => {
    acc[key] = {
      loading: false,
      disabled: false,
    };
    return acc;
  }, {});
  const state = reactive<any>(initState);

  const setLoading = (key: string, loading: boolean = true) => {
    console.log('setLoading', key, loading);
    if (keys.includes(key)) {
      state[key].loading = loading;
      keys.forEach((k) => {
        if (k !== key) {
          state[k].disabled = loading;
        }
      });
    }
  };

  // 重置
  const resetLoading = () => {
    keys.forEach((key) => {
      state[key].loading = false;
      state[key].disabled = false;
    });
  };

  return [state, { setLoading, resetLoading }];
}
