import type { ComputedRef, Ref } from 'vue';
import { computed, unref, h } from 'vue';
import { type StepsProps, type StepProps } from 'ant-design-vue';
import {
  CreateMapReturn,
  DefinitionSteps,
  DefinitionStepsValue,
  CreateMapReturnValue,
} from '#/utils';
import { find, findIndex, map } from 'lodash-es';
import { LoadingOutlined } from '@ant-design/icons-vue';

interface StepStatusConfig {
  arr: CreateMapReturn;
  definition: DefinitionSteps;
  status: ComputedRef<string> | Ref<string>;
}

type returnType = [ComputedRef<StepsProps>];
const loadingIcon = h(LoadingOutlined, { style: { fontSize: '25px' } });
export function useStatusStep({ arr, definition, status }: StepStatusConfig): returnType {
  const stepProps = computed<StepsProps>(() => {
    const current = findIndex<DefinitionStepsValue>(definition, (item) => {
      return item.types.includes(unref(status));
    });
    const items = map<DefinitionStepsValue, StepProps>(definition, (item, index: number) => {
      const info: StepProps = {};
      info.title = item.title;
      if (index === current) {
        const getmap = find<CreateMapReturnValue>(arr, (i) => {
          return i.value === unref(status);
        });
        if (getmap) {
          info.status = getmap.extend;
          info.description = getmap.label;
          if (getmap.extend === 'process') {
            info.icon = loadingIcon;
          }
        }
      }
      return info;
    });
    return {
      current,
      items,
    };
  });

  return [stepProps];
}
