<template>
  <LoginFormTitle v-show="getShow" class="enter-x" />
  <Form
    class="p-4 enter-x"
    :model="formData"
    :rules="getFormRules"
    ref="formRef"
    v-show="getShow"
    @keypress.enter="handleLogin"
  >
    <FormItem class="enter-x">
      <Select
        size="large"
        placeholder="租户选择"
        v-model:value="tenantId"
        :options="tenantList"
        class="fix-auto-fill"
        :fieldNames="{
          label: 'name',
          value: 'id',
        }"
      />
    </FormItem>
    <FormItem name="account" class="enter-x">
      <Input
        size="large"
        v-model:value="formData.account"
        :placeholder="t('sys.login.userName')"
        class="fix-auto-fill"
      />
    </FormItem>
    <FormItem name="password" class="enter-x">
      <InputPassword
        size="large"
        visibilityToggle
        v-model:value="formData.password"
        :placeholder="t('sys.login.password')"
      />
    </FormItem>

    <ARow class="enter-x">
      <ACol :span="12">
        <FormItem>
          <!-- No logic, you need to deal with it yourself -->
          <Checkbox v-model:checked="rememberMe" size="small">
            {{ t('sys.login.rememberMe') }}
          </Checkbox>
        </FormItem>
      </ACol>
      <ACol :span="12">
        <FormItem :style="{ 'text-align': 'right' }">
          <!-- No logic, you need to deal with it yourself -->
          <Button type="link" size="small" @click="setLoginState(LoginStateEnum.RESET_PASSWORD)">
            {{ t('sys.login.forgetPassword') }}
          </Button>
        </FormItem>
      </ACol>
    </ARow>

    <FormItem class="enter-x">
      <Button type="primary" size="large" block @click="handShowVerify" :loading="loading">
        {{ t('sys.login.loginButton') }}
      </Button>
      <!-- <Button size="large" class="mt-4 enter-x" block @click="handleRegister">
        {{ t('sys.login.registerButton') }}
      </Button> -->
    </FormItem>
    <ARow v-if="false" class="enter-x" :gutter="[16, 16]">
      <ACol :md="8" :xs="24">
        <Button block @click="setLoginState(LoginStateEnum.MOBILE)">
          {{ t('sys.login.mobileSignInFormTitle') }}
        </Button>
      </ACol>
      <ACol :md="8" :xs="24">
        <Button block @click="setLoginState(LoginStateEnum.QR_CODE)">
          {{ t('sys.login.qrSignInFormTitle') }}
        </Button>
      </ACol>
      <ACol :md="8" :xs="24">
        <Button block @click="setLoginState(LoginStateEnum.REGISTER)">
          {{ t('sys.login.registerButton') }}
        </Button>
      </ACol>
    </ARow>
  </Form>
</template>
<script lang="ts" setup>
  import { reactive, ref, unref, computed } from 'vue';
  import { getTenantList } from '@/api/admin/tenant';
  import { Checkbox, Form, Input, Row, Col, Button, message, Select } from 'ant-design-vue';
  import SildeVerify from '@/components/SlideVerify';
  import LoginFormTitle from './LoginFormTitle.vue';

  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import projectSetting from '@/settings/projectSetting';
  import { useUserStore } from '@/store/modules/user';
  import { find } from 'lodash-es';
  import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
  // import { useDesign } from '@/hooks/web/useDesign';
  // import { getClient } from '@/settings/clientSetting';
  //import { onKeyStroke } from '@vueuse/core';

  const ACol = Col;
  const ARow = Row;
  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const { t } = useI18n();
  // const { notification, createErrorModal } = useMessage();
  // const { prefixCls } = useDesign('login');
  const { notification } = useMessage();
  const userStore = useUserStore();
  const { useLoginVerify } = projectSetting;

  const { setLoginState, getLoginState } = useLoginState();
  const { getFormRules } = useFormRules();

  const formRef = ref();
  const loading = ref(false);
  const rememberMe = ref(false);
  const tenantList = ref<any[]>([]);
  const tenantId = ref<string | undefined>(undefined);
  const clientId = ref<string>('pig');

  const formData = reactive({
    account: 'admin',
    password: '123456',
  });

  const { validForm } = useFormValid(formRef);

  //onKeyStroke('Enter', handleLogin);

  const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

  async function handleLogin(code = '') {
    const data = await validForm();
    if (!data) return;
    try {
      loading.value = true;
      const userInfo = await userStore.login({
        password: data.password,
        username: data.account,
        mode: 'none', //不要默认的错误提示
        code,
        randomStr: 'blockPuzzle',
        clientId: clientId.value,
        tenantId: tenantId.value as string,
      });
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.username}`,
          duration: 3,
        });
      }
    } catch (error) {
      console.log('登录错误', error);
      // 获取其中的错误信息
      const msg = (error as unknown as Error).message || t('sys.api.networkExceptionMsg');
      notification.error({
        message: t('sys.api.errorTip'),
        description: msg,
        duration: 3,
      });
    } finally {
      loading.value = false;
    }
  }

  async function handShowVerify() {
    const data = await validForm();
    if (!data) return;
    loading.value = true;
    if (useLoginVerify) {
      SildeVerify({
        success: (code) => {
          handleLogin(code);
        },
        fail() {
          loading.value = false;
          console.log('fail');
        },
        cancel() {
          loading.value = false;
          console.log('cancel');
        },
      });
    } else {
      handleLogin();
    }
  }

  const method = {
    // 获取租户列表
    async getTenantList() {
      try {
        let res = await getTenantList();
        tenantList.value = res || [];
        let domain = window.location.host;
        // 使用 find 查找租户
        let tenant = find(res, { tenantDomain: domain });
        if (tenant) {
          tenantId.value = tenant.id;
        } else {
          tenantId.value = tenantList.value[0]?.id;
        }
      } catch (error) {
        message.error('获取租户列表失败');
      }
    },
  };

  method.getTenantList();
</script>
