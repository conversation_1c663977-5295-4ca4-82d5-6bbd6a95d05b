<template>
  <page-wrapper title="列表">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <ButtonSelect
          v-model:value="searchInfo.status"
          class="p-1.5"
          :options="[
            { value: 1, label: `已上架` },
            { value: 0, label: `未上架` },
          ]"
        />
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import ButtonSelect from '@/components/Custom/ButtonSelect.vue';
  import { ref } from 'vue';

  const go = useGo();

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const searchInfo = ref({
    status: 1,
  });

  const [registerTable] = useTable({
    api: async () => ({}),
    columns: [],
    formConfig: {
      schemas: [],
    },
    searchInfo,
    useSearchForm: true,
    actionColumn: {},
  });

  const method = {
    /** 详情 */
    detail: (record: Recordable) => {
      go(`/xxx/${record.id}`);
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        icon: 'ant-design:eye-outlined',
        tooltip: '详情',
        onClick: method.detail.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
