<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="新增编辑">
    <BasicForm @submit="methods.onSubmit" @register="formRegister" />
    <template #leftFooter>
      <ButtonAction :loading="submitLoading" @submit="submit" @cancel="() => go(-1)" />
    </template>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { BasicForm, useForm } from '@/components/Form';
  import ButtonAction from '@/components/Custom/ButtonAction.vue';
  import { DEFAULT_FROM_CONFIG } from '@/components/Form/src/const';
  import { unref, computed, onMounted } from 'vue';

  const go = useGo();
  const route = useRoute();
  const params = route.params;
  const isEdit = computed(() => {
    return !!params.id;
  });

  const { reload: recordInfo, loading } = useApiLoading({
    api: async () => ({}),
    params,
  });

  const { reload, loading: submitLoading } = useApiLoading({
    api: async (_params) => {
      return unref(isEdit)
        ? async (_params) => ({
            tip: 'edit',
          })
        : async (_params) => ({
            tip: 'add',
          });
    },
    immediate: false,
  });

  const [formRegister, { submit, setFieldsValue }] = useForm({
    schemas: [],
    ...DEFAULT_FROM_CONFIG,
  });

  const methods = {
    init() {
      if (isEdit.value) {
        // 这是一个编辑的
        recordInfo().then((res) => {
          setFieldsValue(res || {});
        });
      }
    },
    // businessCycle
    // 提交用户信息
    onSubmit: async (value) => {
      reload(value).then(() => {
        go(-1);
      });
    },
  };

  onMounted(() => {
    methods.init();
  });
</script>

<style lang="less" scoped></style>
