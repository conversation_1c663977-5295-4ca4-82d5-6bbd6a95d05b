import { BasicColumn, FormSchema } from '@/components/Table';
import { useMapWithI18n } from '@/hooks/web/useOnlineI18n';
import { SourceCodeList, OperationSystemList } from '@/maps/prMaps';
import { Description } from '@/components/Description';
import { h } from 'vue';
import { previewImage } from '@/components/RenderVnode';
import { transformRangePicker } from '@/utils/formFn';
import { apiTypeTreeGet } from '@/api/op/pr';

export const basicColumns: BasicColumn[] = [
  {
    title: '产品图片',
    dataIndex: 'image',
    customRender: ({ text }) => {
      return previewImage(text, 'miniTable');
    },
  },
  {
    title: '型号',
    dataIndex: 'model',
  },
  {
    title: '数量',
    dataIndex: 'number',
    customRender: ({ record }) => {
      return h(Description, {
        column: 1,
        schema: [
          {
            label: '库存',
            field: 'stockCount',
          },
          {
            label: '客户',
            field: 'customerCount',
          },
        ],
        bordered: false,
        data: record,
      });
    },
  },
  {
    title: '分类',
    dataIndex: 'typeName',
    customRender: ({ record }) => {
      return h(Description, {
        column: 1,
        schema: [
          {
            label: '来源类别',
            field: 'sourceName',
          },
          {
            label: '产品分类',
            field: 'typeName',
          },
        ],
        bordered: false,
        data: record,
      });
    },
  },
];

export const searchSchema = (): FormSchema[] => [
  {
    field: 'model',
    label: '产品型号',
    component: 'Input',
  },
  {
    field: 'sourceCode',
    label: '来源类别',
    component: 'Select',
    componentProps: {
      options: useMapWithI18n(SourceCodeList),
    },
  },
  {
    field: '[beginTime, endTime]',
    label: '创建时间',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['创建时间起始', '创建时间截止'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    valueFormat: transformRangePicker,
  },
];
export const basicCategorySchema: FormSchema[] = [
  {
    field: 'name',
    fields: ['id', 'pid'],
    label: '分类名称',
    component: 'Input',
    required: true,
  },
];
export const basicSchema: FormSchema[] = [
  {
    field: 'image',
    fields: ['id'],
    label: '产品图片',
    component: 'UploadImage',
  },
  {
    field: 'model',
    label: '产品型号',
    component: 'Input',
  },
  {
    field: 'typeId',
    label: '产品分类',
    component: 'ApiTreeSelect',
    componentProps: {
      api: apiTypeTreeGet,
      valueField: 'id',
      labelField: 'name',
    },
  },
  {
    field: 'sourceCode',
    label: '来源类别',
    component: 'Select',
    componentProps: {
      options: useMapWithI18n(SourceCodeList),
    },
  },
  {
    field: 'system',
    label: '操作系统',
    component: 'Select',
    componentProps: {
      options: useMapWithI18n(OperationSystemList),
    },
  },
  {
    field: 'screensCount',
    label: '屏幕数量',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '2', value: 2 },
        { label: '1', value: 1 },
        { label: '0', value: 0 },
      ],
    },
  },
  {
    field: 'mainScreen',
    label: '主屏分辨率',
    component: 'Input',
  },
  {
    field: 'secondScreen',
    label: '副屏分辨率',
    component: 'Input',
  },
  {
    field: 'mainScreenSize',
    label: '主屏幕尺寸',
    component: 'Input',
  },
  {
    field: 'secondScreenSize',
    label: '副屏幕尺寸',
    component: 'Input',
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
  },
];
