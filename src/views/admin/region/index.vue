<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import { ref } from 'vue';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { searchSchema, basicColumns, basicSchema } from './schema';
  import { apiGetGlobalPageInfo, apiPutGlobal, apiPostGlobal } from '@/api/op/global';

  const go = useGo();

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const searchInfo = ref({
    status: 1,
  });

  const [registerTable, { reload }] = useTable({
    api: apiGetGlobalPageInfo,
    columns: basicColumns,
    formConfig: {
      schemas: searchSchema,
    },
    searchInfo,
    useSearchForm: true,
    actionColumn: {},
  });

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiPostGlobal,
    updateFn: apiPutGlobal,
  });
  const method = {
    /** 详情 */
    detail: (record: Recordable) => {
      go(`/xxx/${record.id}`);
    },
    /** 新增 */
    add: () => {
      addDrawer();
    },
    /** 更新/编辑 */
    update: async (record: Recordable) => {
      updateDrawer({
        record,
      });
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
