import { BasicColumn, FormSchema } from '@/components/Table';

export const basicColumns: BasicColumn[] = [
  {
    title: '等级',
    dataIndex: 'level',
  },
  {
    title: '代码',
    dataIndex: 'code',
  },
  {
    title: '国家区号',
    dataIndex: 'countryNumber',
  },
  {
    title: '国家电话区号',
    dataIndex: 'phoneNumber',
  },
  {
    title: '国家缩写',
    dataIndex: 'abbreviation',
  },
  {
    title: '中文名称',
    dataIndex: 'chineseName',
  },
  {
    title: '外文名称',
    dataIndex: 'foreignName',
  },
  {
    title: '父类id',
    dataIndex: 'parentId',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '关键字搜索',
    component: 'Input',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'level',
    fields: ['id'],
    label: '等级',
    component: 'Input',
  },
  {
    field: 'code',
    label: '代码',
    component: 'Input',
  },
  {
    field: 'countryNumber',
    label: '国家区号',
    component: 'Input',
  },
  {
    field: 'phoneNumber',
    label: '国家电话区号',
    component: 'Input',
  },
  {
    field: 'abbreviation',
    label: '国家缩写',
    component: 'Input',
  },
  {
    field: 'chineseName',
    label: '中文名称',
    component: 'Input',
  },
  {
    field: 'foreignName',
    label: '外文名称',
    component: 'Input',
  },
  {
    field: 'parentId',
    label: '父类id',
    component: 'Input',
  },
  {
    field: 'pinyin',
    label: '拼音',
    component: 'Input',
  },
];
