<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { basicColumns, searchSchema, basicSchema } from './schema';
  import {
    apiGetSocialPage,
    apiAddSocial,
    apiUpdateSocial,
    apiDeleteSocial,
    apiGetSocialById,
  } from '@/api/admin/social';

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const [registerTable, { reload }] = useTable({
    api: apiGetSocialPage,
    columns: basicColumns,
    formConfig: {
      title: '社交登录配置',
      schemas: searchSchema,
    },
    useSearchForm: true,
    actionColumn: {
      width: 160,
    },
  });

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiAddSocial,
    updateFn: apiUpdateSocial,
  });

  const method = {
    /** 新增 */
    add: () => {
      addDrawer();
    },
    /** 更新/编辑 */
    update: async (record: Recordable) => {
      const result = await apiGetSocialById(record.id);
      updateDrawer({
        record: result,
      });
    },
    /** 删除 */
    del: async (record: Recordable) => {
      try {
        await apiDeleteSocial([record.id]);
      } finally {
        reload();
      }
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
