import { BasicColumn, FormSchema } from '@/components/Table';
import { dictToTag } from '@/components/RenderVnode';
import { apiGetDicts } from '@/api/admin/dict';
import { rule } from '@/utils/validate';

export const basicColumns: BasicColumn[] = [
  {
    title: '类型',
    dataIndex: 'type',
    customRender: ({ text }) => {
      return dictToTag({ text, type: 'social_type' });
    },
  },
  {
    title: '应用ID',
    dataIndex: 'appId',
  },
  {
    title: '描述',
    dataIndex: 'remark',
  },
  {
    title: '回调地址',
    dataIndex: 'redirectUrl',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'type',
    label: '类型',
    component: 'ApiSelect',
    componentProps: {
      api: () => apiGetDicts('social_type'),
    },
  },
  {
    field: 'remark',
    label: '描述',
    component: 'Input',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'type',
    fields: ['id'],
    label: '类型',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => apiGetDicts('social_type'),
    },
  },
  {
    field: 'appId',
    label: 'appId',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: 'appId不能为空', trigger: 'blur' },
    ],
  },
  {
    field: 'appSecret',
    label: 'appSecret',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: 'appSecret不能为空', trigger: 'blur' },
    ],
  },
  {
    field: 'redirectUrl',
    label: '回调地址',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '回调地址不能为空', trigger: 'blur' },
      { validator: rule.url, trigger: 'blur' },
    ],
  },
  {
    field: 'remark',
    label: '描述',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '描述不能为空', trigger: 'blur' },
    ],
  },
  {
    field: 'ext',
    label: '扩展信息',
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
    },
  },
];
