<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-space>
          <a-button @click="method.add" type="primary" preIcon="ant-design:plus-outlined">
            新增
          </a-button>
          <!-- <a-button type="primary" preIcon="ant-design:cloud-upload-outlined"> 导入 </a-button> -->
        </a-space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
    <SDrawer title="菜单授权" v-model:open="visible" @on-ok="handleOk">
      <div class="p-[12px]">
        <Tree
          v-model:checkedKeys="checkedKeys"
          :selectedKeys="selectedKeys"
          checkable
          :tree-data="treeData"
          :field-names="fieldNames"
          checkStrictly
      /></div>
    </SDrawer>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { basicColumns, searchSchema, basicSchema } from './schema';
  import {
    apiGetRolePage,
    apiAddRole,
    apiEditRole,
    apiDelRole,
    apiGetRoleById,
    apiRoleMenu,
  } from '@/api/admin/role';
  import { SDrawerForm, useSDrawerForm, SDrawer } from '@/components/SDrawer';
  import { get } from 'lodash-es';
  import { ref } from 'vue';
  import { Tree, type TreeProps } from 'ant-design-vue';
  import { apiGetMenuTree, apiGetMenuByRoleId } from '@/api/admin/menu';

  const visible = ref(false);
  /**
   * ====================
   *       授权逻辑
   * ====================
   */
  const checkedKeys = ref<any>();
  const selectedKeys = ref<string[]>();
  const treeData: any = ref([]);
  const roleId = ref('');
  const fieldNames: TreeProps['fieldNames'] = {
    key: 'id',
    title: 'name',
  };

  // 提交菜单分配
  const handleOk = async () => {
    const menucheckedKeys = checkedKeys.value.checked || checkedKeys.value;
    let data = await apiRoleMenu({
      roleId: roleId.value,
      menuIds: menucheckedKeys.join(','),
    });

    if (data) {
      visible.value = false;
      reload();
    }
  };

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const [registerTable, { reload }] = useTable({
    api: apiGetRolePage,
    columns: basicColumns,
    formConfig: {
      title: '角色管理',
      schemas: searchSchema,
    },
    useSearchForm: true,
    actionColumn: {
      width: 150,
    },
  });

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiAddRole,
    updateFn: apiEditRole,
  });

  const method = {
    /** 新增 */
    add: () => {
      addDrawer();
    },
    /** 编辑 */
    edit: async (record: Recordable) => {
      const result = await apiGetRoleById(record.roleId);
      updateDrawer({
        record: result,
      });
    },
    /* 授权*/
    auth: async (record: Recordable) => {
      // 获取菜单
      console.log(record);
      roleId.value = record.roleId;
      let data = await apiGetMenuTree();
      treeData.value = data;
      checkedKeys.value = (await apiGetMenuByRoleId(roleId.value)) || [];
      visible.value = true;
    },
    /* 删除 */
    del: async (record: Recordable) => {
      try {
        await apiDelRole([record.roleId]);
      } finally {
        reload();
      }
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.edit.bind(null, record),
      },
      {
        label: '授权',
        onClick: method.auth.bind(null, record),
      },
      {
        label: '删除',
        disabled: get(record, 'roleCode', '') === 'ROLE_ADMIN',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
