import { BasicColumn, FormSchema } from '@/components/Table';
import { DsTypeArray } from '@/maps/sysMaps';
import { rule } from '@/utils/validate';
import { validateRoleCode, validateRoleName } from '@/api/admin/role';
import { showToTag } from '@/components/RenderVnode';
import { apiGetDeptTree } from '@/api/admin/dept';
import { apiTreeCheckStrictlyAuto } from '@/utils/formFn';

export const basicColumns: BasicColumn[] = [
  {
    title: '角色名称',
    dataIndex: 'roleName',
  },
  {
    title: '角色标识',
    dataIndex: 'roleCode',
  },
  {
    title: '角色描述',
    dataIndex: 'roleDesc',
  },
  {
    title: '数据权限',
    dataIndex: 'dsType',
    customRender({ text }) {
      return showToTag({ text, arr: DsTypeArray });
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'roleName',
    label: '角色名称',
    component: 'Input',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'roleName',
    fields: ['roleId'],
    label: '角色名称',
    component: 'Input',
    dynamicRules({ model: form }) {
      return [
        { required: true, message: '角色名称不能为空', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
        {
          validator: (rule: any, value: any) => validateRoleName(rule, value, !!form.roleId),
          trigger: 'blur',
        },
      ];
    },
  },
  {
    field: 'roleCode',
    label: '角色标识',
    component: 'Input',
    dynamicRules({ model: form }) {
      return [
        { required: true, message: '角色标识不能为空', trigger: 'blur' },
        { min: 3, message: '长度不能小于3个字符', trigger: 'blur' },
        { validator: rule.validatorCapital, trigger: 'blur' },
        {
          validator: (rule: any, value: any) => validateRoleCode(rule, value, !!form.roleId),
          trigger: 'blur',
        },
      ];
    },
    dynamicDisabled({ model: form }) {
      return !!form.roleId;
    },
  },
  {
    field: 'roleDesc',
    label: '角色描述',
    component: 'InputTextArea',
  },
  {
    field: 'dsType',
    label: '数据权限',
    component: 'Select',
    componentProps: {
      options: DsTypeArray,
    },
    rules: [{ required: true, message: '请选择数据权限类型', trigger: 'blur' }],
    defaultValue: 0,
  },
  {
    field: 'dsScope',
    label: '数据范围',
    component: 'ApiTree',
    componentProps: {
      api: apiGetDeptTree,
      fieldNames: {
        children: 'children',
        title: 'name',
        key: 'id',
      },
      checkable: true,
      valueField: 'id',
      checkStrictly: true,
      hookValue: apiTreeCheckStrictlyAuto,
      defaultExpandAll: true,
    },
    transformGetTo: 'toStr',
    transformSetTo: 'toArr',
    ifShow({ model }) {
      return model.dsType === 1;
    },
  },
];
