<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="addDrawer()" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { ref } from 'vue';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import {
    apiGetMenuTree,
    apiGetMenu,
    apiAddMenu,
    apiUpdateMenu,
    apiDeleteMenu,
  } from '@/api/admin/menu';
  import { columns, searchFormSchema, baseSchema } from './schema';
  import { first } from 'lodash-es';
  import { message } from 'ant-design-vue';

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const searchInfo = ref({
    status: 1,
  });

  const [registerTable, { reload }] = useTable({
    api: apiGetMenuTree,
    columns: columns,
    formConfig: {
      title: '菜单列表',
      schemas: searchFormSchema,
    },
    pagination: false,
    isTreeTable: true,
    searchInfo,
    useSearchForm: true,
    actionColumn: {
      width: 150,
    },
  });

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: baseSchema,
    addFn: apiAddMenu,
    updateFn: apiUpdateMenu,
    destroyOnClose: true,
  });
  const method = {
    /** 更新/编辑 */
    update: async (record: Recordable) => {
      const result = await apiGetMenu({
        menuId: record.id,
      });
      updateDrawer({
        record: first(result),
      });
    },
    /** 新增 */
    add: async (record: Recordable) => {
      addDrawer({
        record: {
          parentId: record.id,
          menuType: '0',
        },
      });
    },
    del: async (record: Recordable) => {
      try {
        let childrenLength = record?.children?.length || 0;
        if (childrenLength === 0) {
          await apiDeleteMenu(record.id);
        } else {
          message.error('请先删除子菜单');
        }
      } finally {
        reload();
      }
    },
  };
  // 可以被显示的新增按钮 menuType
  const showAddBtn = ['0'];
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '新增',
        onClick: method.add.bind(null, record),
        ifShow() {
          return record.menuType && showAddBtn.includes(record.menuType);
        },
      },
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
