<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-space>
          <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
            新增
          </a-button>
          <a-button type="primary" @click="method.handleExpand"> 展开/折叠 </a-button>
        </a-space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { ref } from 'vue';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { basicColumns, basicSchema, searchSchema } from './schema';
  import {
    apiGetDeptTree,
    apiAddDept,
    apiGetDept,
    apiUpdateDept,
    apiDeleteDept,
  } from '@/api/admin/dept';
  import { get, size } from 'lodash-es';
  import { message } from 'ant-design-vue';

  // 展开折叠状态
  const expand = ref(false);

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const [registerTable, { reload, expandAll, collapseAll }] = useTable({
    api: apiGetDeptTree,
    columns: basicColumns,
    formConfig: {
      title: '部门管理',
      schemas: searchSchema,
    },
    isTreeTable: true,
    useSearchForm: true,
    actionColumn: {
      width: 150,
    },
  });

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiAddDept,
    updateFn: apiUpdateDept,
    destroyOnClose: true,
  });
  const method = {
    /** 新增 */
    add: (record: Recordable) => {
      if (record) {
        addDrawer({
          record: {
            parentId: record.id,
          },
        });
      } else {
        addDrawer();
      }
    },
    /** 更新/编辑 */
    update: async (record: Recordable) => {
      const result = await apiGetDept(record.id);
      updateDrawer({
        record: result,
      });
    },
    /** 展开/折叠 */
    handleExpand: () => {
      expand.value = !expand.value;
      if (expand.value) {
        expandAll();
      } else {
        collapseAll();
      }
    },
    /** 删除*/
    del: async (record: Recordable) => {
      try {
        const childrenLength = size(get(record, 'children', []));
        if (childrenLength) {
          message.error('该部门下有子部门，无法删除');
        } else {
          await apiDeleteDept(record.id);
        }
      } finally {
        reload();
      }
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '新增',
        onClick: method.add.bind(null, record),
      },
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
