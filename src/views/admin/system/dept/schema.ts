import { BasicColumn, FormSchema } from '@/components/Table';
import { apiGetDeptTree } from '@/api/admin/dept';

export const basicColumns: BasicColumn[] = [
  {
    title: '部门名称',
    dataIndex: 'name',
  },
  {
    title: '排序',
    dataIndex: 'weight',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'deptName',
    label: '部门名称',
    component: 'Input',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'parentId',
    fields: ['deptId'],
    label: '上级部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: apiGetDeptTree,
      valueField: 'id',
      labelField: 'name',
    },
    ifShow: ({ values }) => values.parentId !== '0',
  },
  {
    field: 'name',
    label: '部门名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    required: true,
  },
];
