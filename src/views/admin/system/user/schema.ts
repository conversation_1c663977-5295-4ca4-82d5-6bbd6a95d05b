import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { tagColorEnum } from '@/enums/colorEnum';
import { apiGetRoleList } from '@/api/admin/role';
import { apiGetPostList } from '@/api/admin/post';
import { apiGetDeptTree } from '@/api/admin/dept';
import { BasicColumn, FormSchema } from '@/components/Table';
import { validatePhone, validateUsername } from '@/api/admin/user';
import { dictToTag } from '@/components/RenderVnode';
import { rule } from '@/utils/validate';
import { apiGetDicts } from '@/api/admin/dict';

export const columns: BasicColumn[] = [
  {
    title: '用户名',
    dataIndex: 'username',
  },
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
  {
    title: '部门',
    dataIndex: 'deptName',
  },
  {
    title: '角色',
    dataIndex: 'roleList',
    customRender: ({ record }) => {
      const { roleList = [] } = record;
      // 取出角色名称
      const roleNameList = roleList?.map((item: any) => item.roleName);
      const tags = roleNameList.map((item: string) => {
        return h(Tag, { color: tagColorEnum.GREEN }, () => item);
      });
      return h('span', tags);
    },
  },
  {
    title: '状态',
    dataIndex: 'lockFlag',
    customRender: ({ text }) => {
      return dictToTag({ text, type: 'lock_flag' });
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'username',
    label: '用户名',
    component: 'Input',
  },
  {
    field: 'phone',
    label: '手机号',
    component: 'Input',
  },
];

export const userSchema: FormSchema[] = [
  {
    field: 'username',
    fields: ['userId'],
    label: '用户名',
    component: 'Input',
    defaultValue: '',
    dynamicRules({ model: form }) {
      return [
        {
          required: true,
          message: '用户名不能为空',
          trigger: 'blur',
        },
        {
          validator: rule.validatorLowerNumber,
          trigger: 'blur',
        },
        {
          validator: (rule: any, value: any) => validateUsername(rule, value, !!form.userId),
          trigger: 'blur',
        },
      ];
    },
    dynamicDisabled({ model: form }) {
      return !!form.userId;
    },
  },
  {
    field: 'password',
    label: '密码',
    component: 'InputPassword',
    required: true,
    defaultValue: '',
    ifShow({ model: form }) {
      return !form.userId;
    },
  },
  {
    field: 'name',
    label: '姓名',
    component: 'Input',
    required: true,
    rules: [{ min: 2, message: '姓名长度不能小于2', trigger: 'blur' }],
  },
  {
    field: 'phone',
    label: '手机号',
    component: 'Input',
    required: true,
    dynamicRules({ model: form }) {
      return [
        { validator: rule.validatePhone, message: '手机号格式错误' },
        {
          validator: (rule: any, value: any) => validatePhone(rule, value, !!form.userId),
          trigger: 'blur',
        },
      ];
    },
    dynamicDisabled({ model: form }) {
      return !!form.userId;
    },
  },
  {
    field: 'role',
    label: '角色',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      mode: 'multiple',
      valueField: 'roleId',
      labelField: 'roleName',
      api: apiGetRoleList,
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'post',
    label: '岗位',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      mode: 'multiple',
      valueField: 'postId',
      labelField: 'postName',
      api: apiGetPostList,
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'deptId',
    label: '部门',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      valueField: 'id',
      labelField: 'name',
      api: apiGetDeptTree,
      getPopupContainer: () => document.body,
    },
  },
  {
    label: '邮箱',
    field: 'email',
    component: 'Input',
  },
  {
    label: '启用',
    field: 'lockFlag',
    component: 'ApiRadioGroup',
    componentProps: {
      api: () => apiGetDicts('lock_flag'),
    },
  },
];

export const resetPasswordSchema: FormSchema[] = [
  {
    field: 'password',
    fields: ['userId', 'username'],
    label: '新密码',
    component: 'InputPassword',
    required: true,
    defaultValue: '',
  },
];
