<template>
  <page-wrapper fixedHeight>
    <Row :gutter="12" class="h-full">
      <Col :span="6">
        <Card style="height: 100%">
          <DeptTree @select="handleSelect" />
        </Card>
      </Col>
      <Col :span="18">
        <Card style="height: 100%">
          <BasicTable @register="registerTable" :searchInfo="searchInfo">
            <template #tableTitle>
              <a-button
                type="primary"
                @click="method.handleCreate"
                preIcon="ant-design:plus-outlined"
              >
                新增用户
              </a-button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'ACTION'">
                <TableAction :actions="tableAction(record)" />
              </template>
            </template>
          </BasicTable>
        </Card>
      </Col>
    </Row>
    <SDrawerForm :maskClosable="false" @register="registerForm" @success="reload()" />
    <SModalForm :maskClosable="false" @register="registerModal" @success="reload()" />
  </page-wrapper>
</template>
<script lang="ts" setup>
  import { reactive } from 'vue';
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { Row, Col, Card } from 'ant-design-vue';
  import DeptTree from './DeptTree.vue';
  import {
    apiUserPage,
    apiUserAdd,
    apiUserEdit,
    apiUserDelete,
    apiUserDetailById,
  } from '@/api/admin/user';
  import { omit } from 'lodash-es';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { SModalForm, useSModalForm } from '@/components/SModal';

  import { columns, searchFormSchema, userSchema, resetPasswordSchema } from './schema';

  defineOptions({ name: 'AccountManagement' });

  const searchInfo = reactive<Recordable>({});
  const [registerTable, { reload }] = useTable({
    api: apiUserPage,
    rowKey: 'userId',
    columns,
    formConfig: {
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    bordered: true,
    actionColumn: {
      width: 180,
    },
  });

  const [registerForm, { addDrawer: addModal, updateDrawer: updateModal }] = useSDrawerForm({
    schemas: userSchema,
    addFn: apiUserAdd,
    updateFn: apiUserEdit,
  });

  const [registerModal, { updateModal: openModalForm }] = useSModalForm({
    schemas: resetPasswordSchema,
    updateFn: apiUserEdit,
  });

  function handleSelect(deptId = '') {
    searchInfo.deptId = deptId;
    reload();
  }

  const method = {
    handleCreate() {
      addModal();
    },
    handleUpdate: async (record: Recordable) => {
      // 获取用户详情
      const result = await apiUserDetailById(record.userId);
      result.post = result?.postList?.map((item: any) => item.postId) || [];
      result.role = result?.roleList?.map((item: any) => item.roleId) || [];
      updateModal({
        record: result,
        merge(v: any) {
          if (v.phone) {
            if (v.phone.indexOf('*') > 0) {
              v = omit(v, 'phone');
            }
          }
          return v;
        },
      });
    },
    async handleDelete(record: Recordable) {
      await apiUserDelete(record.userId);
      reload();
    },
    /** 重置密码 */
    async handleResetPassword(record: Recordable) {
      // 获取用户详情
      const result = await apiUserDetailById(record.userId);
      openModalForm({
        title: '重置密码',
        record: result,
      });
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.handleUpdate.bind(null, record),
      },
      {
        label: '密码',
        onClick: method.handleResetPassword.bind(null, record),
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>
