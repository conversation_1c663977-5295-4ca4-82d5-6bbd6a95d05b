import { validatePostName, validatePostCode } from '@/api/admin/post';
import { BasicColumn, FormSchema } from '@/components/Table';

export const basicColumns: BasicColumn[] = [
  {
    title: '岗位编码',
    dataIndex: 'postCode',
  },
  {
    title: '岗位名称',
    dataIndex: 'postName',
  },
  {
    title: '岗位排序',
    dataIndex: 'postSort',
  },
  {
    title: '岗位描述',
    dataIndex: 'remark',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'postName',
    label: '岗位名称',
    component: 'Input',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'postName',
    fields: ['postId'],
    label: '岗位名称',
    component: 'Input',
    required: true,
    dynamicRules: ({ model: form }) => {
      return [
        { required: true, message: '岗位名称不能为空', trigger: 'blur' },
        { min: 2, message: '岗位名称长度不能小于2', trigger: 'blur' },
        {
          validator: (rule: any, value: any) => validatePostName(rule, value, !!form.postId),
          trigger: 'blur',
        },
      ];
    },
  },
  {
    field: 'postCode',
    label: '岗位编码',
    component: 'Input',
    required: true,
    dynamicRules: ({ model: form }) => {
      return [
        { required: true, message: '岗位编码不能为空', trigger: 'blur' },
        { min: 2, message: '岗位编码长度不能小于2', trigger: 'blur' },
        {
          validator: (rule: any, value: any) => validatePostCode(rule, value, !!form.postId),
          trigger: 'blur',
        },
      ];
    },
  },
  {
    field: 'postSort',
    label: '岗位排序',
    component: 'InputNumber',
    required: true,
  },
  {
    field: 'remark',
    label: '岗位描述',
    component: 'InputTextArea',
  },
];
