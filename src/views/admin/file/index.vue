<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SModalForm width="600px" @register="registerModalForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { apiGetFileList, apiDeleteFile } from '@/api/admin/file';
  import { basicColumns, searchSchema, basicSchema } from './schema';
  import { SModalForm, useSModalForm } from '@/components/SModal';

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const [registerTable, { reload }] = useTable({
    api: apiGetFileList,
    columns: basicColumns,
    formConfig: {
      schemas: searchSchema,
      title: '文件管理',
    },
    useSearchForm: true,
    actionColumn: true,
  });

  const [registerModalForm, { addModal }] = useSModalForm({
    schemas: basicSchema,
    addFn: () => ({}),
  });
  const method = {
    /** 新增 */
    add: () => {
      addModal();
    },
    delete: async (record: Recordable) => {
      await apiDeleteFile([record.id]);
      reload();
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: method.delete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
