import { BasicColumn, FormSchema } from '@/components/Table';
import { calculateFileSize } from '@/utils/calculateFileSize';

export const basicColumns: BasicColumn[] = [
  {
    title: '文件名称',
    dataIndex: 'original',
  },
  {
    title: 'hash',
    dataIndex: 'hash',
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    customRender: ({ text }) => {
      return calculateFileSize(text);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'original',
    label: '文件名称',
    component: 'Input',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'original',
    component: 'UploadImage',
    help: '请上传文件',
  },
];
