import { BasicColumn, FormSchema } from '@/components/Table';
import { dictToTag } from '@/components/RenderVnode';
import { rule } from '@/utils/validate';
import { apiValidateDictType, apiValidateDictItemLabel } from '@/api/admin/dict';

// 字典类型表格列定义
export const dictColumns: BasicColumn[] = [
  {
    title: '字典类型',
    dataIndex: 'dictType',
    width: 120,
  },
  {
    title: '描述',
    dataIndex: 'description',
  },
  {
    title: '系统内置',
    dataIndex: 'systemFlag',
    width: 100,
    customRender: ({ text }) => {
      return dictToTag({ text, type: 'dict_type' });
    },
  },
  {
    title: '备注',
    dataIndex: 'remarks',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

// 字典类型搜索表单
export const dictSearchSchema: FormSchema[] = [
  {
    field: 'dictType',
    label: '字典类型',
    component: 'Input',
  },
  {
    field: 'description',
    label: '描述',
    component: 'Input',
  },
];

// 字典类型编辑表单
export const dictFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'dictType',
    fields: ['id'],
    label: '字典类型',
    component: 'Input',
    required: true,
    dynamicRules: ({ model: form }) => {
      return [
        { validator: rule.overLength, trigger: 'blur' },
        { required: true, message: '类型不能为空', trigger: 'blur' },
        { validator: rule.validatorNameCn, trigger: 'blur' },
        {
          validator: (rule: any, value: any, callback: any) =>
            apiValidateDictType(rule, value, callback, !!form.id),
          trigger: 'blur',
        },
      ];
    },
  },
  {
    field: 'description',
    label: '描述',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '描述不能为空', trigger: 'blur' },
    ],
  },
  {
    field: 'systemFlag',
    label: '系统内置',
    component: 'RadioGroup',
    required: true,
    defaultValue: '0',
    componentProps: {
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
    },
  },
  {
    field: 'remarks',
    label: '备注',
    component: 'InputTextArea',
    rules: [{ validator: rule.overLength, trigger: 'blur' }],
  },
];

// 字典项表格列定义
export const dictItemColumns: BasicColumn[] = [
  {
    title: '字典类型',
    dataIndex: 'dictType',
    width: 120,
  },
  {
    title: '数据值',
    dataIndex: 'value',
    width: 100,
  },
  {
    title: '标签',
    dataIndex: 'label',
    width: 120,
  },
  {
    title: '描述',
    dataIndex: 'description',
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    width: 80,
  },
  {
    title: '备注',
    dataIndex: 'remarks',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

// 字典项编辑表单
export const dictItemFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'dictId',
    label: '字典ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'dictType',
    label: '字典类型',
    component: 'Input',
    required: true,
    dynamicDisabled: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '请点选左侧字典项', trigger: 'blur' },
    ],
  },
  {
    field: 'value',
    label: '数据值',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '数据值不能为空', trigger: 'blur' },
    ],
  },
  {
    field: 'label',
    fields: ['dictType', 'id'],
    label: '标签',
    component: 'Input',
    required: true,
    dynamicRules: ({ model: form }) => {
      return [
        { validator: rule.overLength, trigger: 'blur' },
        { required: true, message: '标签不能为空', trigger: 'blur' },
        {
          validator: (rule: any, value: any, callback: any) =>
            apiValidateDictItemLabel(rule, value, callback, form.dictType, !!form.id),
          trigger: 'blur',
        },
      ];
    },
  },
  {
    field: 'description',
    label: '描述',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '描述不能为空', trigger: 'blur' },
    ],
  },
  {
    field: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    required: true,
    defaultValue: 0,
    rules: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  },
  {
    field: 'remarks',
    label: '备注',
    component: 'InputTextArea',
    rules: [{ validator: rule.overLength, trigger: 'blur' }],
  },
];
