<template>
  <page-wrapper>
    <template #header>
      <a-space>
        <a-button
          type="primary"
          size="small"
          @click="handleAddDict"
          preIcon="ant-design:plus-outlined"
        >
          新增
        </a-button>
        <a-button
          type="primary"
          size="small"
          @click="handleRefreshCache"
          preIcon="ant-design:sync-outlined"
        >
          刷新缓存
        </a-button>
        <a-button
          type="primary"
          size="small"
          @click="handleExport"
          preIcon="ant-design:download-outlined"
        >
          导出
        </a-button>
      </a-space>
    </template>

    <SidebarLayout fillBackground left-title="字典类型" :right-title="currentDictTitle">
      <template #left>
        <AnimatedTabs
          :list="treeData"
          v-model:model-value="activeKey"
          v-model:model-item="currentDict"
          :field-names="{ title: 'description', key: 'id' }"
        >
          <template #actions>
            <a-input-search
              v-model:value="searchValue"
              placeholder="请输入字典类型或描述"
              class="mb-3"
              @search="handleSearch"
          /></template>
          <template #item="{ item }">
            <div class="flex flex-row justify-between items-center w-full pagedict-item px-[6px]">
              <div class="pagedict-item-title">{{ item.description }}</div>
              <div class="pagedict-item-des">{{ item.dictType }}</div>
              <div class="pagedict-item-actions" @click.stop>
                <a-button type="link" size="small" @click="handleEditDict(item)"> 编辑 </a-button>
                <Popconfirm title="确定删除此字典类型吗？" @confirm="handleDeleteDict(item.id)">
                  <a-button type="text" size="small" danger :disabled="item.systemFlag === '1'">
                    删除
                  </a-button>
                </Popconfirm>
              </div>
            </div>
          </template>
        </AnimatedTabs>
      </template>
      <template #right>
        <!-- 右侧字典项列表 -->
        <div v-if="activeKey">
          <BasicTable @register="registerItemTable">
            <template #tableTitle>
              <a-button
                type="primary"
                size="small"
                @click="handleAddDictItem"
                preIcon="ant-design:plus-outlined"
              >
                新增字典项
              </a-button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'ACTION'">
                <TableAction :actions="itemTableAction(record)" />
              </template>
            </template>
          </BasicTable>
        </div>
        <Empty v-else description="请选择左侧字典类型" />
      </template>
    </SidebarLayout>

    <!-- 字典类型编辑抽屉 -->
    <SDrawerForm @register="registerDictForm" @success="handleDictFormSuccess" />

    <!-- 字典项编辑抽屉 -->
    <SDrawerForm @register="registerDictItemForm" @success="handleDictItemFormSuccess" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import SidebarLayout from '@/components/Custom/SidebarLayout.vue';
  import AnimatedTabs from '@/components/Custom/AnimatedTabs.vue';
  import { ref, computed } from 'vue';
  import { message, Popconfirm, Empty } from 'ant-design-vue';
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import {
    apiFetchList,
    apiFetchItemList,
    apiAddObj,
    apiPutObj,
    apiDelObj,
    apiAddItemObj,
    apiPutItemObj,
    apiDelItemObj,
    apiRefreshCache,
    apiExportDict,
  } from '@/api/admin/dict';
  import { dictFormSchema, dictItemColumns, dictItemFormSchema } from './schema';
  import { downloadByData } from '@/utils/file/download';
  import { useApiLoading } from '@/hooks/web/useApiLoading';

  // 定义类型
  interface DictItem {
    id: string;
    dictType: string;
    description: string;
    systemFlag: string;
    remarks?: string;
    createTime: string;
  }

  // 响应式数据
  const searchValue = ref('');
  const activeKey = ref<string | number>('');
  const currentDict = ref<DictItem | null>(null);

  // 计算属性
  const currentDictTitle = computed(() => {
    return currentDict.value ? `字典项管理 - ${currentDict.value.description}` : '字典项管理';
  });

  const { reload: loadTreeData, apiResult: treeData } = useApiLoading({
    api: apiFetchList,
  });

  // 字典类型表格配置
  const [registerDictForm, { addDrawer: addDictDrawer, updateDrawer: updateDictDrawer }] =
    useSDrawerForm({
      schemas: dictFormSchema,
      addFn: apiAddObj,
      updateFn: apiPutObj,
      title: '字典类型',
    });

  // 字典项表格配置
  const [registerItemTable, { reload: reloadItemTable }] = useTable({
    api: apiFetchItemList,
    columns: dictItemColumns,
    useSearchForm: false,
    actionColumn: {},
    searchInfo: {
      dictId: activeKey,
    },
    // 撑满
    canResize: false,
  });

  const [
    registerDictItemForm,
    { addDrawer: addDictItemDrawer, updateDrawer: updateDictItemDrawer },
  ] = useSDrawerForm({
    schemas: dictItemFormSchema,
    addFn: apiAddItemObj,
    updateFn: apiPutItemObj,
    title: '字典项',
  });

  // 搜索处理
  const handleSearch = (value: string) => {
    console.log('搜索:', value);
    loadTreeData({
      name: value,
    });
  };

  // 新增字典类型
  const handleAddDict = () => {
    addDictDrawer();
  };

  // 编辑字典类型
  const handleEditDict = (record: any) => {
    updateDictDrawer({ record });
  };

  // 删除字典类型
  const handleDeleteDict = async (id: string) => {
    try {
      await apiDelObj([id]);
      // 如果删除的是当前选中的字典，清空右侧
      if (currentDict.value?.id === id) {
        currentDict.value = null;
        activeKey.value = '';
      }
    } catch (error) {
      console.error('删除失败:', error);
      await loadTreeData();
    }
  };

  // 刷新缓存
  const handleRefreshCache = () => {
    apiRefreshCache();
  };

  // 导出功能
  const handleExport = async () => {
    try {
      const result = await apiExportDict({});
      downloadByData(result.data, 'dict.xlsx');
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  // 新增字典项
  const handleAddDictItem = () => {
    if (!currentDict.value) {
      message.warning('请先选择字典类型');
      return;
    }
    addDictItemDrawer({
      record: {
        dictId: currentDict.value.id,
        dictType: currentDict.value.dictType,
      },
    });
  };

  // 字典项表格操作
  const itemTableAction = (record: Recordable): ActionItem[] => {
    return [
      {
        label: '编辑',
        onClick: () => updateDictItemDrawer({ record }),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除此字典项吗？',
          confirm: () => apiDelItemObj(record.id),
        },
      },
    ];
  };

  // 字典类型表单成功回调
  const handleDictFormSuccess = () => {
    loadTreeData();
  };

  // 字典项表单成功回调
  const handleDictItemFormSuccess = () => {
    reloadItemTable();
  };
</script>

<style lang="less" scoped>
  .pagedict {
    &-item {
      height: 22px;

      &:hover {
        .pagedict-item-des {
          display: none;
        }

        .pagedict-item-actions {
          display: inline-block;
        }
      }

      &-title {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-des {
        display: inline-block;
      }

      &-actions {
        display: none;
      }
    }
  }
</style>
