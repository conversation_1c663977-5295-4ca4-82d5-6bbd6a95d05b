import { BasicColumn, FormSchema } from '@/components/Table';
import { dictToTag } from '@/components/RenderVnode';
import { apiValidateParamName, apiValidateParamCode } from '@/api/admin/param';
import { apiGetDicts } from '@/api/admin/dict';

export const columns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'publicName',
  },
  {
    title: '键',
    dataIndex: 'publicKey',
  },
  {
    title: '值',
    dataIndex: 'publicValue',
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return dictToTag({ text, type: 'status_type' });
    },
  },
  {
    title: '业务类型',
    dataIndex: 'systemFlag',
    customRender: ({ text }) => {
      return dictToTag({ text, type: 'dict_type' });
    },
  },

  {
    title: '类型',
    dataIndex: 'publicType',
    customRender: ({ text }) => {
      return dictToTag({ text, type: 'param_type' });
    },
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'publicName',
    component: 'Input',
    label: '名称',
  },
  {
    field: 'publicKey',
    component: 'Input',
    label: '键',
  },
  {
    field: 'systemFlag',
    component: 'ApiSelect',
    label: '业务类型',
    componentProps: {
      api: () => apiGetDicts('dict_type'),
    },
  },
];
export const formSchema: FormSchema[] = [
  {
    field: 'publicName',
    component: 'Input',
    label: '名称',
    dynamicRules: ({ model: form }) => {
      return [
        {
          validator: (rule: any, value: any) => apiValidateParamName(rule, value, !!form.publicId),
          trigger: 'blur',
        },
        {
          required: true,
          message: '请输入名称',
          trigger: 'blur',
        },
      ];
    },
  },
  {
    field: 'publicKey',
    component: 'Input',
    label: '键',
    dynamicRules: ({ model: form }) => {
      return [
        {
          pattern: /^[A-Z_]+$/,
          message: '键必须为大写字母和下划线',
          trigger: 'blur',
        },
        {
          validator: (rule: any, value: any) => apiValidateParamCode(rule, value, !!form.publicId),
          trigger: 'blur',
        },
        {
          required: true,
          message: '请输入键',
          trigger: 'blur',
        },
      ];
    },
  },
  {
    field: 'publicType',
    fields: ['publicId'],
    component: 'ApiSelect',
    label: '类型',
    required: true,
    componentProps: {
      api: () => apiGetDicts('param_type'),
    },
  },
  {
    field: 'publicValue',
    component: 'Input',
    label: '值',
    required: true,
  },

  {
    field: 'validateCode',
    component: 'Input',
    label: '编码',
  },

  {
    field: 'systemFlag',
    component: 'ApiRadioGroup',
    label: '业务类型',
    required: true,
    componentProps: {
      api: apiGetDicts,
      params: 'dict_type',
    },
  },
  {
    field: 'status',
    component: 'ApiRadioGroup',
    label: '状态',
    required: true,
    componentProps: {
      api: apiGetDicts,
      params: 'status_type',
    },
  },
];
