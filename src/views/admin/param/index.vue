<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { columns, searchSchema, formSchema } from './schema';
  import {
    apiGetParamPage,
    apiDeleteParam,
    apiRefreshParamCache,
    apiAddParam,
    apiUpdateParam,
  } from '@/api/admin/param';
  import { useApiLoading } from '@/hooks/web/useApiLoading';

  const { loading, reload: refreshCache } = useApiLoading({
    api: apiRefreshParamCache,
    immediate: false,
  });

  const [registerTable, { reload }] = useTable({
    columns,
    rowKey: 'publicId',
    api: apiGetParamPage,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
    actionColumn: {
      width: 160,
    },
  });

  const [registerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: formSchema,
    addFn: apiAddParam,
    updateFn: apiUpdateParam,
  });

  // 操作部分

  const method = {
    handleAdd(_record: any) {
      addDrawer();
    },
    handleUpdate(record: any) {
      updateDrawer({
        record,
      });
    },
    async handleDelete(record: any) {
      await apiDeleteParam([record.publicId]);
      reload();
    },
    async handleAsync() {
      await refreshCache();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        onClick: method.handleUpdate.bind(null, record),
        label: '编辑',
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-space>
          <a-button type="primary" @click="method.handleAdd" preIcon="ant-design:plus-outlined" />
          <a-button
            :loading
            type="primary"
            @click="method.handleAsync"
            preIcon="ant-design:sync-outlined"
          >
            刷新同步
          </a-button>
        </a-space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerForm" @success="reload()" />
  </page-wrapper>
</template>

<style lang="less" scoped></style>
