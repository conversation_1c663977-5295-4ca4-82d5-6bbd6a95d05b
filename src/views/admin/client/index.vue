<template>
  <page-wrapper>
    <BasicTable @register="registerTable" @selection-change="handleSelectionChange">
      <template #tableTitle>
        <a-space>
          <a-button type="primary" @click="method.handleAdd" preIcon="ant-design:plus-outlined">
            新增
          </a-button>
          <a-button
            :loading="refreshLoading"
            type="primary"
            @click="method.handleRefreshCache"
            preIcon="ant-design:sync-outlined"
          >
            刷新缓存
          </a-button>
          <a-button
            :disabled="!hasSelected"
            type="primary"
            danger
            @click="method.handleBatchDelete"
            preIcon="ant-design:delete-outlined"
          >
            批量删除
          </a-button>
        </a-space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { columns, searchSchema, formSchema } from './schema';
  import {
    apiGetClientPage,
    apiAddClient,
    apiUpdateClient,
    apiDeleteClient,
    apiRefreshClientCache,
    apiGetClientById,
  } from '@/api/admin/client';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { Modal } from 'ant-design-vue';

  const { loading: refreshLoading, reload: refreshCache } = useApiLoading({
    api: apiRefreshClientCache,
    immediate: false,
  });

  // 选中的行
  const selectedRowKeys = ref<string[]>([]);
  const hasSelected = ref(false);

  const [registerTable, { reload }] = useTable({
    api: apiGetClientPage,
    rowKey: 'id',
    columns,
    formConfig: {
      schemas: searchSchema,
    },
    useSearchForm: true,
    rowSelection: {
      type: 'checkbox',
    },
    actionColumn: {
      width: 150,
    },
  });

  // 处理选择变化
  function handleSelectionChange({ keys }: { keys: string[] }) {
    selectedRowKeys.value = keys;
    hasSelected.value = keys.length > 0;
  }

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: formSchema,
    addFn: apiAddClient,
    updateFn: apiUpdateClient,
  });

  const method = {
    /** 新增 */
    handleAdd() {
      addDrawer();
    },
    /** 更新/编辑 */
    async handleUpdate(record: Recordable) {
      // 获取客户端详情
      const result = await apiGetClientById(record.clientId);
      updateDrawer({
        record: result,
      });
    },
    /** 删除 */
    async handleDelete(record: Recordable) {
      await apiDeleteClient([record.id]);
      reload();
    },
    /** 批量删除 */
    async handleBatchDelete() {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
        onOk: async () => {
          await apiDeleteClient(selectedRowKeys.value);
          selectedRowKeys.value = [];
          hasSelected.value = false;
          reload();
        },
      });
    },
    /** 刷新缓存 */
    async handleRefreshCache() {
      await refreshCache();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.handleUpdate.bind(null, record),
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
