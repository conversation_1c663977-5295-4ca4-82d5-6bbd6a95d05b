import { BasicColumn, FormSchema } from '@/components/Table';
import { dictToTag } from '@/components/RenderVnode';
import { apiGetDicts } from '@/api/admin/dict';
import { apiValidateClientId } from '@/api/admin/client';
import { rule } from '@/utils/validate';

export const columns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '客户端ID',
    dataIndex: 'clientId',
  },
  {
    title: '客户端密钥',
    dataIndex: 'clientSecret',
  },
  {
    title: '域',
    dataIndex: 'scope',
  },
  {
    title: '授权模式',
    dataIndex: 'authorizedGrantTypes',
    width: 400,
    customRender: ({ text }) => {
      return dictToTag({ text, type: 'grant_types' });
    },
  },
  {
    title: '令牌时效(秒)',
    dataIndex: 'accessTokenValidity',
  },
  {
    title: '刷新时效(秒)',
    dataIndex: 'refreshTokenValidity',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'clientId',
    label: '客户端ID',
    component: 'Input',
  },
  {
    field: 'clientSecret',
    label: '客户端密钥',
    component: 'Input',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'clientId',
    fields: ['id'],
    label: '客户端ID',
    component: 'Input',
    required: true,
    dynamicRules: ({ model: form }) => {
      return [
        { validator: rule.overLength, trigger: 'blur' },
        { required: true, message: '编号不能为空', trigger: 'blur' },
        { validator: rule.validatorLowercase, trigger: 'blur' },
        {
          validator: (rule: any, value: any) => apiValidateClientId(rule, value, !!form.id),
          trigger: 'blur',
        },
      ];
    },
  },
  {
    field: 'clientSecret',
    label: '客户端密钥',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '密钥不能为空', trigger: 'blur' },
      { validator: rule.validatorLower, trigger: 'blur' },
    ],
  },
  {
    field: 'scope',
    label: '域',
    component: 'Input',
    required: true,
    defaultValue: 'server',
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '域不能为空', trigger: 'blur' },
    ],
  },
  {
    field: 'authorizedGrantTypes',
    label: '授权模式',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      mode: 'multiple',
      api: () => apiGetDicts('grant_types'),
    },
  },
  {
    field: 'webServerRedirectUri',
    label: '回调地址',
    component: 'Input',
    required: true,
    rules: [
      { validator: rule.overLength, trigger: 'blur' },
      { required: true, message: '回调地址不能为空', trigger: 'blur' },
      { validator: rule.url, trigger: 'blur' },
    ],
  },
  {
    field: 'authorities',
    label: '权限',
    component: 'Input',
    rules: [{ validator: rule.overLength, trigger: 'blur' }],
  },
  {
    field: 'accessTokenValidity',
    label: '令牌时效(秒)',
    component: 'InputNumber',
    required: true,
    defaultValue: 43200,
    componentProps: {
      min: 1,
    },
    rules: [
      { required: true, message: '令牌时效不能为空', trigger: 'blur' },
      { type: 'number', min: 1, message: '令牌时效不能小于一小时', trigger: 'blur' },
    ],
  },
  {
    field: 'refreshTokenValidity',
    label: '刷新时效(秒)',
    component: 'InputNumber',
    required: true,
    defaultValue: 2592001,
    componentProps: {
      min: 1,
    },
    rules: [
      { required: true, message: '刷新时效不能为空', trigger: 'blur' },
      { type: 'number', min: 1, message: '刷新时效不能小于两小时', trigger: 'blur' },
    ],
  },
  {
    field: 'autoapprove',
    label: '自动放行',
    component: 'ApiRadioGroup',
    required: true,
    defaultValue: 'true',
    componentProps: {
      api: () => apiGetDicts('common_status'),
    },
  },
  {
    field: 'onlineQuantity',
    label: '同时在线数量',
    component: 'InputNumber',
    required: true,
    defaultValue: 1,
    componentProps: {
      min: 1,
    },
  },
  {
    field: 'captchaFlag',
    label: '验证码校验',
    component: 'ApiRadioGroup',
    required: true,
    defaultValue: '1',
    componentProps: {
      api: () => apiGetDicts('captcha_flag_types'),
    },
  },
  {
    field: 'encFlag',
    label: '密码加密传输',
    component: 'ApiRadioGroup',
    required: true,
    defaultValue: '1',
    componentProps: {
      api: () => apiGetDicts('enc_flag_types'),
    },
  },
];
