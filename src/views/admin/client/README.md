# 客户端管理页面

## 功能说明

本页面实现了完整的客户端管理功能，包括：

### 列表功能

- ✅ 序号显示
- ✅ 客户端ID显示
- ✅ 客户端密钥显示
- ✅ 域显示
- ✅ 授权模式显示（使用字典标签）
- ✅ 令牌时效显示
- ✅ 刷新时效显示
- ✅ 行选择功能（复选框）

### 操作功能

- ✅ 新增客户端
- ✅ 编辑客户端
- ✅ 删除客户端（带确认）
- ✅ 批量删除（带确认）
- ✅ 刷新缓存

### 搜索功能

- ✅ 按客户端ID搜索
- ✅ 按域搜索

### 表单验证

- ✅ 客户端ID：必填、小写字母下划线、长度限制、唯一性验证
- ✅ 客户端密钥：必填、小写字母、长度限制
- ✅ 域：必填、长度限制
- ✅ 授权模式：必选（多选）
- ✅ 回调地址：必填、URL格式验证、长度限制
- ✅ 权限：长度限制
- ✅ 令牌时效：必填、数字、最小值验证
- ✅ 刷新时效：必填、数字、最小值验证
- ✅ 自动放行：必选
- ✅ 同时在线数量：必填、数字、最小值验证
- ✅ 验证码校验：必选
- ✅ 密码加密传输：必选

### 字典使用

- `grant_types` - 授权模式
- `common_status` - 通用状态
- `captcha_flag_types` - 验证码标志类型
- `enc_flag_types` - 加密标志类型

## 技术实现

### 文件结构

- `index.vue` - 主页面组件
- `schema.ts` - 表格列定义和表单配置

### 主要组件

- `BasicTable` - 数据表格
- `SDrawerForm` - 抽屉表单
- `TableAction` - 表格操作按钮
- `ApiSelect` - API选择器
- `ApiRadioGroup` - API单选组
- `InputNumber` - 数字输入框

### API接口

- `apiGetClientPage` - 获取分页列表
- `apiAddClient` - 新增客户端
- `apiUpdateClient` - 更新客户端
- `apiDeleteClient` - 删除客户端
- `apiRefreshClientCache` - 刷新缓存
- `apiGetClientById` - 获取详情
- `apiValidateClientId` - 验证客户端ID

## 使用说明

1. 页面加载后自动显示客户端列表
2. 点击"新增"按钮打开新增表单
3. 点击表格行的"编辑"按钮打开编辑表单
4. 点击表格行的"删除"按钮删除记录（需确认）
5. 点击"刷新缓存"按钮刷新服务端缓存
6. 使用搜索表单可以按条件筛选数据
