<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-space>
          <a-button type="primary" @click="handleExport" preIcon="ant-design:export-outlined">
            导出
          </a-button>
        </a-space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record as RoutelogItem)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerDes @register="registerDrawerDes" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useMessage } from '@/hooks/web/useMessage';
  import { basicColumns, searchSchema, detailSchema } from './schema';
  import { apiGetLogPage, apiDeleteLog, apiExportLog } from '@/api/admin/log';
  import { RoutelogItem } from '@/api/admin/model/logModel';
  import { downloadByData } from '@/utils/file/download';
  import { SDrawerDes, useSDrawerDes } from '@/components/SDrawer';

  const { createMessage } = useMessage();

  const [registerTable, { reload }] = useTable({
    api: apiGetLogPage,
    columns: basicColumns,
    formConfig: {
      title: '日志查询',
      schemas: searchSchema,
    },
    useSearchForm: true,
    actionColumn: {},
    defSort: {
      descs: 'create_time',
    },
    sortFn: (sorter) => {
      const { field, order } = sorter;
      if (field === 'createTime') {
        if (order === 'ascend') {
          return {
            descs: 'create_time',
            ascs: '',
          };
        } else {
          return {
            descs: '',
            ascs: 'create_time',
          };
        }
      }
    },
  });

  const [registerDrawerDes, { openDrawer }] = useSDrawerDes({
    schema: detailSchema,
  });

  /**
   * 查看详情
   */
  async function handleDetail(record: RoutelogItem) {
    try {
      openDrawer({
        title: '日志详情',
        record,
      });
    } catch (error) {
      createMessage.error('获取日志详情失败');
    }
  }

  /**
   * 删除日志
   */
  async function handleDelete(record: RoutelogItem) {
    try {
      await apiDeleteLog([record.id]);
    } finally {
      reload();
    }
  }

  /**
   * 导出日志
   */
  async function handleExport() {
    try {
      const result = await apiExportLog({});
      downloadByData(result.data, '日志数据.xlsx');
      createMessage.success('导出成功');
    } catch (error) {
      createMessage.error('导出失败');
    }
  }

  /**
   * 表格操作
   */
  function tableAction(record: RoutelogItem): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '确定删除吗？',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
