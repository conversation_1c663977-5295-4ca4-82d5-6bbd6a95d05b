import { dictToTag, textToTag } from '@/components/RenderVnode';
import { BasicColumn, FormSchema } from '@/components/Table';
import { DescItem } from '@/components/Description';
import { apiGetDicts } from '@/api/admin/dict';
import dayjs from 'dayjs';

export const basicColumns: BasicColumn[] = [
  {
    title: '类型',
    dataIndex: 'logType',
    customRender: ({ text }) => {
      return dictToTag({ text, type: 'log_type' });
    },
  },
  {
    title: '标题',
    dataIndex: 'title',
  },
  {
    title: 'Ip地址',
    dataIndex: 'remoteAddr',
  },
  {
    title: '请求方式',
    dataIndex: 'method',
    customRender: ({ text }) => {
      return textToTag({ text });
    },
  },
  {
    title: '耗时(ms)',
    dataIndex: 'time',
    customRender: ({ text }) => {
      return text ? `${text}/ms` : '';
    },
  },
  {
    title: '请求时间',
    dataIndex: 'createTime',
    sorter: true,
  },
  {
    title: '操作人',
    dataIndex: 'createBy',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'logType',
    label: '日志类型',
    component: 'ApiSelect',
    componentProps: {
      api: () => apiGetDicts('log_type'),
    },
  },
  {
    field: 'createTime',
    label: '时间',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始时间', '结束时间'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    valueFormat: ({ value }) => {
      const [start, end] = value;
      const newStart = dayjs(start).format('YYYY-MM-DD 00:00:00');
      const newEnd = dayjs(end).format('YYYY-MM-DD 23:59:59');
      return [newStart, newEnd];
    },
  },
];

export const basicSchema: FormSchema[] = [];

export const detailSchema: DescItem[] = [
  {
    label: '标题',
    field: 'title',
  },
  {
    label: '类型',
    field: 'logType',
    render: (text) => {
      return dictToTag({ text, type: 'log_type' });
    },
  },

  {
    label: '请求方式',
    field: 'method',
    render: (text) => {
      return textToTag({ text });
    },
  },
  {
    label: 'Ip地址',
    field: 'remoteAddr',
  },
  {
    label: '耗时(ms)',
    field: 'time',
    render: (text) => {
      return `${text}/ms`;
    },
  },
  {
    label: '请求URI',
    field: 'requestUri',
  },
  {
    label: '请求时间',
    field: 'createTime',
  },
  {
    label: '操作人',
    field: 'createBy',
  },
  {
    label: '请求参数',
    field: 'params',
  },
];
//
