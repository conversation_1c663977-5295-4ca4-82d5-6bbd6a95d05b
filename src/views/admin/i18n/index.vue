<template>
  <page-wrapper>
    <SidebarLayout fillBackground>
      <template #right-title>
        <a-space>
          <span>{{ currentDictTitle }}</span>
        </a-space>
      </template>
      <template #left-title>
        <a-space>
          <span>国际目录</span>
          <a-button
            type="primary"
            size="small"
            @click="addI18nCode()"
            preIcon="ant-design:plus-outlined"
          >
            新增
          </a-button>
        </a-space>
      </template>
      <template #left>
        <AnimatedTabs
          :list="treeData"
          v-model:model-value="activeKey"
          v-model:model-item="currentDict"
          :field-names="{ title: 'currently', key: 'id' }"
        >
          <template #actions>
            <a-input-search
              v-model:value="searchValue"
              placeholder="请输入名称搜索"
              class="mb-3"
              @search="handleSearch"
          /></template>
          <template #item="{ item }">
            <div class="flex flex-row justify-between items-center w-full pagedict-item px-[6px]">
              <div class="pagedict-item-title">{{ item.currently }}</div>
              <div class="pagedict-item-des">{{ item.code }}</div>
              <div class="pagedict-item-actions" @click.stop>
                <a-button type="link" size="small" @click="handleCopyDict(item)"> 复制 </a-button>
                <a-button type="link" size="small" @click="handleEditDict(item)"> 编辑 </a-button>
                <Popconfirm title="确定删除此字典类型吗？" @confirm="handleDeleteDict(item.id)">
                  <a-button type="text" size="small" danger :disabled="item.systemFlag === '1'">
                    删除
                  </a-button>
                </Popconfirm>
              </div>
            </div>
          </template>
          <template #footer>
            <Pagination size="small" v-bind="pagination" />
          </template>
        </AnimatedTabs>
      </template>
      <template #right>
        <!-- 右侧字典项列表 -->
        <div v-if="activeKey">
          <BasicTable @register="registerItemTable">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'ACTION'">
                <TableAction :actions="itemTableAction(record)" />
              </template>
            </template>
          </BasicTable>
        </div>
        <Empty v-else description="请选择左侧字典类型" />
      </template>
    </SidebarLayout>

    <SModalForm @register="registerI18nCodeForm" @success="handleDictFormSuccess" />

    <!-- 字典项编辑抽屉 -->
    <SDrawerForm @register="registerDictItemForm" @success="handleDictItemFormSuccess" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import SidebarLayout from '@/components/Custom/SidebarLayout.vue';
  import AnimatedTabs from '@/components/Custom/AnimatedTabs.vue';
  import { ref, computed } from 'vue';
  import { Popconfirm, Empty, Pagination, message } from 'ant-design-vue';
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { i18nSchema, basicColumns, itemSchema } from './schema';
  import { usePageData } from '@/hooks/component/usePageData';
  import {
    apiGetI18nPage,
    apiGetI18nInfo,
    apiAddI18n,
    apiEditI18n,
    apiEditI18nItem,
    apiDelI18n,
  } from '@/api/admin/i18n';
  import { SModalForm, useSModalForm } from '@/components/SModal';
  import { useClipboard } from '@vueuse/core';

  // 响应式数据
  const searchValue = ref('');
  const activeKey = ref<string | number>('');
  const currentDict = ref<any | null>(null);

  // 初始化剪贴板功能
  const { copy, isSupported } = useClipboard();

  // 计算属性
  const currentDictTitle = computed(() => {
    return currentDict.value ? `国际化数据- ${currentDict.value.currently}` : '国际化数据';
  });

  const {
    reload: loadTreeData,
    list: treeData,
    pagination,
  } = usePageData({
    api: apiGetI18nPage,
  });

  // 字典类型表格配置
  const [registerI18nCodeForm, { addModal: addI18nCode, updateModal: updateI18nCode }] =
    useSModalForm({
      schemas: i18nSchema,
      addFn: apiAddI18n,
      updateFn: apiEditI18n,
      addText: '新增国际化',
      updateText: '编辑国际化',
    });

  // 字典项表格配置
  const [registerItemTable, { reload: reloadItemTable }] = useTable({
    api: apiGetI18nInfo,
    fetchSetting: {
      listField: 'items',
    },
    columns: basicColumns,
    useSearchForm: false,
    pagination: false,
    actionColumn: {},
    searchInfo: {
      id: activeKey,
    },
    // 撑满
    canResize: false,
  });

  const [registerDictItemForm, { updateDrawer: updateItemDrawer }] = useSDrawerForm({
    schemas: itemSchema,
    updateFn: apiEditI18nItem,
  });

  // 搜索处理
  const handleSearch = (value: string) => {
    loadTreeData({
      keyword: value,
    });
  };

  // 编辑字典类型
  const handleEditDict = (record: any) => {
    updateI18nCode({ record });
  };
  // 复制字典类型
  const handleCopyDict = async (record: any) => {
    if (!isSupported) {
      message.error('当前浏览器不支持复制功能');
      return;
    }

    try {
      await copy(record.code);
      message.success('复制成功');
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败');
    }
  };

  // 删除字典类型
  const handleDeleteDict = async (id: string) => {
    try {
      await apiDelI18n([id]);
      // 如果删除的是当前选中的字典，清空右侧
      if (currentDict.value?.id === id) {
        currentDict.value = null;
        activeKey.value = '';
      }
    } catch (error) {
      console.error('删除失败:', error);
    } finally {
      await loadTreeData();
    }
  };

  // 字典项表格操作
  const itemTableAction = (record: Recordable): ActionItem[] => {
    return [
      {
        label: '编辑',
        onClick: () => updateItemDrawer({ record }),
      },
    ];
  };

  // 字典类型表单成功回调
  const handleDictFormSuccess = () => {
    loadTreeData();
    reloadItemTable();
  };

  // 字典项表单成功回调
  const handleDictItemFormSuccess = () => {
    reloadItemTable();
  };
</script>

<style lang="less" scoped>
  .pagedict {
    &-item {
      height: 22px;

      &:hover {
        .pagedict-item-des {
          display: none;
        }

        .pagedict-item-actions {
          display: inline-block;
        }
      }

      &-title {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-des {
        display: inline-block;
      }

      &-actions {
        display: none;
      }
    }
  }
</style>
