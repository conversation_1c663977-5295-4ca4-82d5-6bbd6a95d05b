import { BasicColumn, FormSchema } from '@/components/Table';

// 字典类型编辑表单
export const i18nSchema: FormSchema[] = [
  {
    field: 'code',
    fields: ['id'],
    label: '编码',
    component: 'Input',
    required: true,
    dynamicDisabled({ model }) {
      return !!model.id;
    },
  },
  {
    field: 'zhCn',
    label: '值',
    component: 'Input',
    required: true,
  },
];

// 字典项表格列定义
export const basicColumns: BasicColumn[] = [
  {
    title: '语言',
    dataIndex: 'language',
    width: 100,
  },
  {
    title: '值',
    dataIndex: 'value',
  },
];

// 字典项编辑表单
export const itemSchema: FormSchema[] = [
  {
    field: 'language',
    label: '语言',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    field: 'value',
    fields: ['id'],
    label: '数据值',
    component: 'InputTextArea',
    required: true,
    rules: [{ required: true, message: '数据值不能为空', trigger: 'blur' }],
  },
];
