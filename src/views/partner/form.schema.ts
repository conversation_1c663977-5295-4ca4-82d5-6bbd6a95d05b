import { FormSchema } from '@/components/Table';

export const basicSchema: FormSchema[] = [
  {
    field: 'companyName',
    fields: ['id'], // 需要关联查询的字段
    label: '伙伴公司全称',
    component: 'Input',
    required: true,
  },
  {
    field: 'companyLogo',
    label: '公司Logo',
    component: 'UploadImage',
  },
  {
    field: 'companyShort',
    label: '客户简称',
    component: 'Input',
    required: true,
  },
  {
    field: 'linkman',
    label: '联系人',
    component: 'Input',
    required: true,
  },
  {
    field: '[country,province,city]',
    label: '所在地区',
    component: 'RegionSelect',
    required: true,
  },
  {
    field: 'mobile',
    label: '联系电话',
    component: 'Input',
    required: true,
  },
  {
    field: 'address',
    label: '详细地址',
    component: 'Input',
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
  },
  {
    field: 'introduction',
    label: '公司介绍',
    component: 'InputTextArea',
  },
  {
    field: 'taxNumber',
    label: '公司税号',
    component: 'Input',
  },
  {
    field: 'username',
    label: '登录账号',
    component: 'Input',
    required: true,
  },
  {
    field: 'bankCode',
    label: '开户行',
    component: 'Input',
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
  },
  {
    field: 'corporateAccount',
    label: '对公账户',
    component: 'Input',
  },
];
