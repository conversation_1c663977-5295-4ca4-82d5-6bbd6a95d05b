export enum tagColorEnum {
  YELLOW = '#FFB000',
  ORANGE = '#FF851D',
  RED = '#FF4C52',
  PURPLE = '#A57AFA',
  BLUE = '#1F90FF',
  GREEN = '#26C7B7',
  LIME = '#71C922',
  GRAY = '#E5C48A',
  //默认颜色
  DEFAULT_BUTTON = '#9DAFC0',
}

export enum cardColorEnum {
  '#FF4C52',
  '#5155C6',
  '#FF851D',
  '#FFB000',
  '#3F8DF9',
  '#95DFA6',
  '#26C7B7',
  '#71C922',
  '#E5C48A',
  '#44566C',
}

export enum textColorEnum {
  YELLOW = '#FFB000',
  ORANGE = '#FF851D',
  RED = '#FF4C52',
  PURPLE = '#A57AFA',
  BLUE = '#1F90FF',
  GREEN = '#26C7B7',
  LIME = '#71C922',
  GRAY = '#E5C48A',
  //默认颜色
  DEFAULT_BUTTON = '#9DAFC0',
}

export enum statusColorsEnum {
  pink = 'pink',
  red = 'red',
  yellow = 'yellow',
  orange = 'orange',
  cyan = 'cyan',
  green = 'green',
  blue = 'blue',
  purple = 'purple',
  geekblue = 'geekblue',
  magenta = 'magenta',
  volcano = 'volcano',
  gold = 'gold',
  lime = 'lime',
}

export enum EchartsColorEnum {
  '#37A2DA',
  '#FFDB5C',
  '#E062AE',
  '#9d96f5',
  '#32C5E9',
  '#fb7293',
  '#67E0E3',
  '#9FE6B8',
  '#ff9f7f',
  '#E690D1',
  '#e7bcf3',
  '#8378EA',
  '#96BFFF',
}
